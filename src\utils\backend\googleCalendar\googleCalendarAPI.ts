import { supabase } from "@/utils/backend/supabase";

/**
 * Google Calendar API utility functions for accommodation management
 * These functions implement the Google Calendar synchronization feature
 * as specified in the documentation.
 */

export type GoogleAccount = {
  id: string;
  account_email: string;
};

export type Calendar = {
  id: string;
  summary: string;
};

export type AccommodationDetails = {
  id: string;
  host_id: string;
  accommodation_name: string;
  is_google_calendar_sync_enabled: boolean;
  google_calendar_id: string | null;
  host_google_account_id: string | null;
  host_google_accounts?: GoogleAccount;
};

/**
 * Function 1: Initiate the Connection
 * Purpose: To start the Google OAuth process that allows a host to grant calendar permissions.
 * Permissions: Host(owner) or Admin. The "Connect" button should only be visible to the owner of the accommodation.
 */
export async function startGoogleConnection(
  currentPagePath: string
): Promise<void> {
  // Check if user is authenticated with Supabase first
  const {
    data: { session },
  } = await supabase.auth.getSession();
  if (!session) {
    throw new Error("User not authenticated. Please log in first.");
  }

  // Use the Next.js API route that handles authentication properly
  const startUrl = `/api/google-oauth-start?redirect_to=${encodeURIComponent(
    currentPagePath
  )}`;
  window.location.href = startUrl;
}

/**
 * Function 2: Check Connection Status
 * Purpose: To get a fresh list of Google accounts the host has connected.
 * This is useful for updating the UI after the user returns from the Google OAuth flow.
 * Permissions: Host(owner) or Admin. (owner of the accommodation.)
 */
export async function getConnectedGoogleAccounts(): Promise<GoogleAccount[]> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) return [];

    const { data, error } = await supabase
      .from("host_google_accounts")
      .select("id, account_email")
      .eq("host_id", user.id);

    return error ? [] : data;
  } catch (error) {
    console.error("Error fetching connected Google accounts:", error);
    return [];
  }
}

/**
 * Function 3: Fetch Calendar List
 * Purpose: To get a list of all available calendars from a specific connected account.
 * Permissions: Host or Admin. (owner of the accommodation.)
 * Parameter: hostGoogleAccountId (string): The ID of the account from getConnectedGoogleAccounts.
 */
export async function fetchCalendarList(
  hostGoogleAccountId: string
): Promise<Calendar[] | null> {
  try {
    const { data, error } = await supabase.functions.invoke(
      "get-google-calendars",
      {
        body: { hostGoogleAccountId },
      }
    );

    return error ? null : data.calendars;
  } catch (error) {
    console.error("Error fetching calendar list:", error);
    return null;
  }
}

/**
 * Function 4: Save the Selection
 * Purpose: To save the host's final choice of calendar to the specific accommodation record.
 * Permissions: Host or Admin. (owner of the accommodation.)
 * Parameters: accommodationId (string), hostGoogleAccountId (string), googleCalendarId (string)
 */
export async function linkCalendarToAccommodation(
  accommodationId: string,
  hostGoogleAccountId: string,
  googleCalendarId: string
): Promise<{ error: any }> {
  try {
    const { error } = await supabase
      .from("accommodations")
      .update({
        host_google_account_id: hostGoogleAccountId,
        google_calendar_id: googleCalendarId,
        is_google_calendar_sync_enabled: true,
      })
      .eq("id", accommodationId);

    return { error };
  } catch (error) {
    console.error("Error linking calendar to accommodation:", error);
    return { error };
  }
}

/**
 * Additional utility function: Get Accommodation Details
 * Purpose: To fetch the details for a single accommodation, including its sync status.
 * This function is useful for determining the initial state of the calendar sync.
 */
export async function getAccommodationDetails(
  accommodationId: string
): Promise<{
  accommodation: AccommodationDetails | null;
  error: any;
}> {
  try {
    const { data: accommodation, error } = await supabase
      .from("accommodations")
      .select(
        `
        id,
        host_id,
        accommodation_name,
        is_google_calendar_sync_enabled,
        google_calendar_id,
        host_google_account_id,
        host_google_accounts (
          id,
          account_email
        )
      `
      )
      .eq("id", accommodationId)
      .single();

    if (error) {
      console.error("Error fetching accommodation details:", error);
      return { accommodation: null, error };
    }

    return { accommodation, error: null };
  } catch (error) {
    console.error("Error in getAccommodationDetails:", error);
    return { accommodation: null, error };
  }
}

/**
 * Additional utility function: Disconnect Calendar
 * Purpose: To disconnect the Google Calendar from the accommodation.
 * This removes the calendar sync and resets the related fields.
 */
export async function disconnectCalendarFromAccommodation(
  accommodationId: string
): Promise<{ error: any }> {
  try {
    const { error } = await supabase
      .from("accommodations")
      .update({
        host_google_account_id: null,
        google_calendar_id: null,
        is_google_calendar_sync_enabled: false,
      })
      .eq("id", accommodationId);

    return { error };
  } catch (error) {
    console.error("Error disconnecting calendar from accommodation:", error);
    return { error };
  }
}

/**
 * Utility function to check if user has permission to manage accommodation
 * Purpose: To verify that the current user is the owner or admin of the accommodation
 */
export async function checkAccommodationPermission(
  accommodationId: string
): Promise<boolean> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) return false;

    // Get user role
    const { data: userProfile } = await supabase
      .from("users")
      .select("user_role")
      .eq("id", user.id)
      .single();

    // Admins have access to all accommodations
    if (userProfile?.user_role === "admin") {
      return true;
    }

    // Check if user is the host/owner of this accommodation
    const { data: accommodation } = await supabase
      .from("accommodations")
      .select("host_id")
      .eq("id", accommodationId)
      .single();

    return accommodation?.host_id === user.id;
  } catch (error) {
    console.error("Error checking accommodation permission:", error);
    return false;
  }
}
