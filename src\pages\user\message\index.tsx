import React from "react";
import { Flex } from "@chakra-ui/react";
import { Message } from "@/components/message/Message";
import { useMessagesStore } from "@/store/user/messages/messagesStore";
import { useUserAuthStore } from "@/store/user/users/userAuthStore";

const Index = () => {
  const usersMessagesToCurrentUser = useMessagesStore(
    (state) => state.usersMessagesToCurrentUser
  );

  const currentUser = useUserAuthStore((state) => state.currentUser);
  const messages = usersMessagesToCurrentUser(currentUser?.id || "");

  return (
    <Flex flexDirection={"column"}>
      {currentUser &&
        messages.map((item) => (
          <Message key={item.id} message={item} currentUser={currentUser} />
        ))}
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
