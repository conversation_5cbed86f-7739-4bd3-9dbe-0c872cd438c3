export type ReservationType = {
  id: string;
  start: Date;
  end: Date;
  createdAt: Date;
  updatedAt: Date;
  data: {
    reserved: {
      userId: string;
      guestHouseId: string;
      totalPrice: number;
      numberOfGuests: number;
      checkInTime: string;
      checkOutTime: string;
      specialRequests: string;
      status: "pending" | "confirmed" | "cancelled" | "completed";
    };
    blackout?: {
      name: string;
    };
  };
};
