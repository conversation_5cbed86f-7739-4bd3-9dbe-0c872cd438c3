import { ReservationType } from "@/types/ReservationType";

const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}.${month}.${day}`;
};

export const formatReservationDate = (reservation: ReservationType) => {
  const startDate = formatDate(reservation.start);
  const endDate = formatDate(reservation.end);
  return `${startDate} 〜 ${endDate}`;
};
