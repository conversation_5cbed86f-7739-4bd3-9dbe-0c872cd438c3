import { Flex } from "@chakra-ui/react";
import React from "react";
import { getAccommodationsList } from "@/utils/backend/accommodations/getAccommodationsList";
import { AccommodationPreview } from "@/components/Accommodation/AccommodationPreview";
import { LoadingComponent } from "@/components/ui/LoadingComponent";
import { NoAccommodationFound } from "@/components/Accommodation/NoAccommodationFound";
import { ErrorLoadingAccommodations } from "@/components/Accommodation/ErrorLoadingAccommodations";
import { Accommodation } from "@/types/AccommodationType";

const Index = () => {
  const [accommodations, setAccommodations] = React.useState<
    Accommodation[] | null
  >(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchAccommodations = async () => {
      try {
        setLoading(true);
        const { accommodations: data, error } = await getAccommodationsList();

        if (error) {
          setError(error.message);
          setAccommodations([]);
        } else {
          setAccommodations(data || []);
          setError(null);
        }
      } catch (err) {
        setError("Failed to fetch accommodations");
        setAccommodations([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAccommodations();
  }, []);

  if (error) {
    return <ErrorLoadingAccommodations error={error} />;
  }

  if (loading) {
    return <LoadingComponent />;
  }

  if (!accommodations?.length) {
    return <NoAccommodationFound />;
  }

  return (
    <Flex flexDirection={"column"} px={".5rem"}>
      {accommodations?.map((accommodation: any) => (
        <AccommodationPreview
          key={accommodation.id}
          accommodation={accommodation}
        />
      ))}
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
