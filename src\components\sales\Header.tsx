import React from "react";
import { Flex, Text } from "@chakra-ui/react";
import { formatAmount } from "@/utils/sales/formatAmount";
import { useSalesStore } from "@/store/user/sales/salesStore";

export const Header = () => {
  const { overallOverview } = useSalesStore();

  return (
    <Flex
      flexDirection={"row"}
      justifyContent={"space-between"}
      pb={"1rem"}
      borderBottom={"1px solid"}
      borderColor={"gray.100"}
    >
      <Flex alignItems={"center"} gap={"1rem"}>
        <Text fontWeight={"600"} fontSize={"1.3rem"}>
          総予約数
        </Text>
        <Text fontWeight={"600"} fontSize={"1.2rem"}>
          {overallOverview.numberOfReservations}件
        </Text>
      </Flex>
      <Flex w={"14rem"} flexDirection={"column"}>
        <Flex w={"100%"} justifyContent={"space-between"}>
          <Text fontWeight={"600"} fontSize={"1.2rem"}>
            予約総額
          </Text>
          <Text fontWeight={"400"} fontSize={"1.2rem"}>
            {formatAmount(overallOverview.revenue)}
          </Text>
        </Flex>
        <Flex w={"100%"} justifyContent={"space-between"}>
          <Text fontWeight={"600"} fontSize={"1.2rem"}>
            精算見込額
          </Text>
          <Text
            color={overallOverview.profit > 0 ? "secondary" : "red.500"}
            fontWeight={"400"}
            fontSize={"1.2rem"}
          >
            {formatAmount(overallOverview.profit)}
          </Text>
        </Flex>
      </Flex>
    </Flex>
  );
};
