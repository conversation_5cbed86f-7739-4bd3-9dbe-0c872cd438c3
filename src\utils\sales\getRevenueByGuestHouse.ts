import { getRevenue } from "@/utils/sales/getRevenue";
import { getGrossProfit } from "@/utils/sales/getGrossProfit";
import { GrossProfitByGuesthouseType } from "@/types/sales/grossProfitByGuesthouseType";
import { getTotalExpenses } from "./getGuesthouseExpenses";
import { sampleExpenses } from "@/tempData/expenses";
import { sampleGuestHouses } from "@/tempData/guestHouses";
import { sampleReservations } from "@/tempData/reservations";

export const getGrossProfitByGuestHouse = () => {
  const guesthouses = sampleGuestHouses;
  const reservations = sampleReservations;

  const grossProfitByGuesthouse: GrossProfitByGuesthouseType[] = [];

  guesthouses.forEach((guesthouse) => {
    const list = reservations.filter(
      (r) => r.data.reserved.guestHouseId === guesthouse.id
    );
    const revenue = getRevenue(list);
    const expenses = getTotalExpenses(
      sampleExpenses.filter((item) => item.guestHouseId === guesthouse.id)
    );
    const grossProfit = getGrossProfit({ revenue, expenses });
    grossProfitByGuesthouse.push({
      guesthouseName: guesthouse.name,
      grossProfit,
    });
  });

  return grossProfitByGuesthouse;
};
