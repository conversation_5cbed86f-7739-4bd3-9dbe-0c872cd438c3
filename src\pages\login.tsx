import {
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Image,
  Input,
  Text,
} from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";
import { useUserAuthStore } from "@/store/user/users/userAuthStore";
import { signInAdminUser } from "@/utils/backend/auth/signInAdminUser";

const Login = () => {
  const router = useRouter();
  const currentUser = useUserAuthStore((state) => state.currentUser);
  const setCurrentUser = useUserAuthStore((state) => state.setCurrentUser);

  const [creds, setCreds] = React.useState({
    username: "",
    password: "",
  });

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCreds((prevCreds) => ({
      ...prevCreds,
      [e.target.name]: e.target.value,
    }));
  };

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    signInAdminUser(creds.username, creds.password)
      .then((res) => {
        if (res.user) {
          setCurrentUser(res.user);
          router.push("/user/management");
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  return (
    <Flex
      px={{ base: "1.5rem", md: "5rem" }}
      flexDirection={"column"}
      alignItems={"center"}
      pt={{ base: "5rem", md: "5rem" }}
      minH={"100dvh"}
    >
      <Flex
        w={"100%"}
        flexDirection={"column"}
        gap={"1rem"}
        justifyContent={"center"}
        alignItems={"center"}
      >
        <Flex
          flexDirection={"column"}
          gap={"1rem"}
          justifyContent={"center"}
          alignItems={"center"}
        >
          <Image
            src={"/images/Column.png"}
            alt="Tokyo Guesthouse"
            w={"20rem"}
            h={"auto"}
          />
          <Text
            fontSize={"1.5rem"}
            fontWeight={"semi-bold"}
            letterSpacing={".005em"}
          >
            Please log in to continue
          </Text>
        </Flex>
        <Flex w={"100%"} flexDirection={"column"} gap={"1rem"}>
          <form onSubmit={onSubmit}>
            <FormControl>
              <FormLabel fontWeight={"normal"}>ID</FormLabel>
              <Input
                // type="email"
                name="username"
                placeholder=""
                value={creds.username}
                onChange={onChange}
              />
              <FormErrorMessage>Error</FormErrorMessage>
            </FormControl>
            <FormControl>
              <FormLabel fontWeight={"normal"}>Password</FormLabel>
              <Input
                type="password"
                name="password"
                placeholder=""
                value={creds.password}
                onChange={onChange}
              />
              <FormErrorMessage>Error</FormErrorMessage>
            </FormControl>
            <Button variant={"primary"} mt={"1rem"} type="submit">
              ログイン
            </Button>
          </form>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default Login;
