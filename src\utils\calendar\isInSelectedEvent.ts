import { ReservationType } from "@/types/ReservationType";

type prop = {
  day: Date;
  selectedReservation?: ReservationType;
};

export const isInSelectedEvent = ({ day, selectedReservation }: prop) => {
  if (!selectedReservation) return false;

  const eventStart = new Date(selectedReservation.start);
  const eventEnd = new Date(selectedReservation.end);

  const dayStart = new Date(day.getFullYear(), day.getMonth(), day.getDate());

  const normalizedStart = new Date(
    eventStart.getFullYear(),
    eventStart.getMonth(),
    eventStart.getDate()
  );
  const normalizedEnd = new Date(
    eventEnd.getFullYear(),
    eventEnd.getMonth(),
    eventEnd.getDate()
  );

  return dayStart >= normalizedStart && dayStart <= normalizedEnd;
};
