/**
 * Authentication test page to diagnose login issues
 * Navigate to /debug/auth-test to use this
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Code,
  Alert,
  AlertIcon,
  Input,
  FormControl,
  FormLabel,
  useToast,
  Divider,
  Badge
} from '@chakra-ui/react';
import { supabase } from '@/utils/backend/supabase';
import { useUserAuthStore } from '@/store/user/users/userAuthStore';

const AuthTestPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [authState, setAuthState] = useState<any>(null);
  const [serverTest, setServerTest] = useState<any>(null);
  const toast = useToast();

  const currentUser = useUserAuthStore((state) => state.currentUser);
  const setCurrentUser = useUserAuthStore((state) => state.setCurrentUser);
  const clearUser = useUserAuthStore((state) => state.clearUser);

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      setAuthState({
        session: session ? {
          hasAccessToken: !!session.access_token,
          expiresAt: session.expires_at,
          isExpired: session.expires_at ? session.expires_at < Date.now() / 1000 : null,
          userId: session.user?.id,
          userEmail: session.user?.email,
        } : null,
        user: user ? {
          id: user.id,
          email: user.email,
          createdAt: user.created_at,
        } : null,
        errors: {
          sessionError: sessionError?.message,
          userError: userError?.message,
        },
        zustandUser: currentUser,
      });
    } catch (error) {
      console.error('Auth state check error:', error);
    }
  };

  const testServerAuth = async () => {
    try {
      const response = await fetch('/api/debug/auth-status', {
        credentials: 'include',
      });
      const data = await response.json();
      setServerTest(data);
    } catch (error) {
      setServerTest({ error: error.message });
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      toast({
        title: 'Missing Credentials',
        description: 'Please enter both email and password',
        status: 'warning',
        duration: 3000,
      });
      return;
    }

    setIsLoading(true);
    try {
      // Try Supabase login
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        // Update Zustand store
        setCurrentUser({
          id: data.user.id,
          full_name: data.user.email || 'Unknown',
          user_role: 'host', // Default role
          avatar_url: null,
        } as any);

        toast({
          title: 'Login Successful',
          description: `Logged in as ${data.user.email}`,
          status: 'success',
          duration: 3000,
        });

        // Refresh auth state
        await checkAuthState();
      }
    } catch (error: any) {
      toast({
        title: 'Login Failed',
        description: error.message || 'Unknown error',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      clearUser();
      await checkAuthState();
      setServerTest(null);
      
      toast({
        title: 'Logged Out',
        description: 'Successfully logged out',
        status: 'success',
        duration: 3000,
      });
    } catch (error: any) {
      toast({
        title: 'Logout Error',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    }
  };

  const testGoogleOAuth = async () => {
    try {
      const response = await fetch('/api/google-oauth-start?redirect_to=/debug/auth-test', {
        credentials: 'include',
      });

      if (response.ok) {
        toast({
          title: 'OAuth Test Success',
          description: 'Google OAuth endpoint is working',
          status: 'success',
          duration: 3000,
        });
      } else {
        const errorData = await response.json();
        toast({
          title: `OAuth Test Failed (${response.status})`,
          description: errorData.details || errorData.error,
          status: 'error',
          duration: 8000,
        });
      }
    } catch (error: any) {
      toast({
        title: 'OAuth Test Error',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    }
  };

  return (
    <Box maxW="800px" mx="auto" p={6}>
      <VStack spacing={6} align="stretch">
        <Text fontSize="2xl" fontWeight="bold">
          🔧 Authentication Test Page
        </Text>

        {/* Login Form */}
        <Box p={4} borderWidth={1} borderRadius="md">
          <Text fontSize="lg" fontWeight="bold" mb={4}>
            Supabase Login Test
          </Text>
          <VStack spacing={3}>
            <FormControl>
              <FormLabel>Email</FormLabel>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
              />
            </FormControl>
            <FormControl>
              <FormLabel>Password</FormLabel>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
              />
            </FormControl>
            <HStack spacing={2}>
              <Button onClick={handleLogin} isLoading={isLoading} colorScheme="blue">
                Login with Supabase
              </Button>
              <Button onClick={handleLogout} variant="outline">
                Logout
              </Button>
            </HStack>
          </VStack>
        </Box>

        {/* Auth State Display */}
        <Box p={4} borderWidth={1} borderRadius="md">
          <HStack justify="space-between" mb={4}>
            <Text fontSize="lg" fontWeight="bold">
              Current Auth State
            </Text>
            <Button size="sm" onClick={checkAuthState}>
              Refresh
            </Button>
          </HStack>
          
          {authState && (
            <VStack align="stretch" spacing={2}>
              <HStack>
                <Text fontWeight="medium">Client Session:</Text>
                <Badge colorScheme={authState.session ? 'green' : 'red'}>
                  {authState.session ? 'Present' : 'Missing'}
                </Badge>
              </HStack>
              
              <HStack>
                <Text fontWeight="medium">Client User:</Text>
                <Badge colorScheme={authState.user ? 'green' : 'red'}>
                  {authState.user ? authState.user.email : 'Missing'}
                </Badge>
              </HStack>
              
              <HStack>
                <Text fontWeight="medium">Zustand Store:</Text>
                <Badge colorScheme={authState.zustandUser ? 'green' : 'red'}>
                  {authState.zustandUser ? 'Has User' : 'Empty'}
                </Badge>
              </HStack>

              <Code p={2} fontSize="sm" whiteSpace="pre-wrap">
                {JSON.stringify(authState, null, 2)}
              </Code>
            </VStack>
          )}
        </Box>

        {/* Server Test */}
        <Box p={4} borderWidth={1} borderRadius="md">
          <HStack justify="space-between" mb={4}>
            <Text fontSize="lg" fontWeight="bold">
              Server Auth Test
            </Text>
            <Button size="sm" onClick={testServerAuth}>
              Test Server
            </Button>
          </HStack>
          
          {serverTest && (
            <Code p={2} fontSize="sm" whiteSpace="pre-wrap" maxH="200px" overflowY="auto">
              {JSON.stringify(serverTest, null, 2)}
            </Code>
          )}
        </Box>

        {/* Google OAuth Test */}
        <Box p={4} borderWidth={1} borderRadius="md">
          <Text fontSize="lg" fontWeight="bold" mb={4}>
            Google OAuth Test
          </Text>
          <Button onClick={testGoogleOAuth} colorScheme="green">
            Test Google OAuth Endpoint
          </Button>
        </Box>

        {/* Instructions */}
        <Alert status="info">
          <AlertIcon />
          <Box>
            <Text fontWeight="bold">Instructions:</Text>
            <Text fontSize="sm">
              1. Enter your Supabase credentials and click "Login with Supabase"<br/>
              2. Check that both Client Session and Client User show as "Present"<br/>
              3. Click "Test Server" to verify server can see your session<br/>
              4. If server test passes, try "Test Google OAuth Endpoint"<br/>
              5. If all tests pass, the Google Calendar integration should work
            </Text>
          </Box>
        </Alert>
      </VStack>
    </Box>
  );
};

export default AuthTestPage;

AuthTestPage.getInitialProps = async () => {
  return {
    layoutType: "blank",
  };
};
