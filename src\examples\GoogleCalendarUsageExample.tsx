/**
 * Example component showing how to use the Google Calendar API functions
 * This is for reference only and demonstrates the recommended usage patterns
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Button, Box, Text, useToast } from '@chakra-ui/react';
import {
  startGoogleConnection,
  getConnectedGoogleAccounts,
  fetchCalendarList,
  linkCalendarToAccommodation,
  getAccommodationDetails,
  disconnectCalendarFromAccommodation,
  checkAccommodationPermission,
  type GoogleAccount,
  type Calendar,
  type AccommodationDetails
} from '@/utils/backend/googleCalendar/googleCalendarAPI';

export const GoogleCalendarUsageExample = () => {
  const router = useRouter();
  const toast = useToast();
  const accommodationId = router.query['res-id'] as string;

  const [isLoading, setIsLoading] = useState(false);
  const [accommodation, setAccommodation] = useState<AccommodationDetails | null>(null);
  const [connectedAccounts, setConnectedAccounts] = useState<GoogleAccount[]>([]);
  const [calendars, setCalendars] = useState<Calendar[]>([]);
  const [hasPermission, setHasPermission] = useState(false);

  // Check permissions and load initial data
  useEffect(() => {
    if (accommodationId) {
      initializeComponent();
    }
  }, [accommodationId]);

  const initializeComponent = async () => {
    setIsLoading(true);
    try {
      // 1. Check if user has permission to manage this accommodation
      const permission = await checkAccommodationPermission(accommodationId);
      setHasPermission(permission);

      if (!permission) {
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to manage this accommodation.',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      // 2. Get accommodation details to check current sync status
      const { accommodation: accommodationData, error: accommodationError } = 
        await getAccommodationDetails(accommodationId);
      
      if (accommodationError) {
        throw new Error('Failed to fetch accommodation details');
      }
      
      setAccommodation(accommodationData);

      // 3. Get connected Google accounts
      const accounts = await getConnectedGoogleAccounts();
      setConnectedAccounts(accounts);

    } catch (error) {
      console.error('Error initializing component:', error);
      toast({
        title: 'Initialization Error',
        description: 'Failed to load Google Calendar data.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle starting Google OAuth connection
  const handleStartConnection = () => {
    if (!hasPermission) return;
    
    const currentPagePath = router.asPath;
    startGoogleConnection(currentPagePath);
    // Note: This will redirect the user, so no further code will execute
  };

  // Handle fetching calendars for a specific account
  const handleFetchCalendars = async (hostGoogleAccountId: string) => {
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      const calendarList = await fetchCalendarList(hostGoogleAccountId);
      
      if (!calendarList) {
        throw new Error('Failed to fetch calendars');
      }
      
      setCalendars(calendarList);
      
      toast({
        title: 'Calendars Loaded',
        description: `Found ${calendarList.length} calendars.`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error fetching calendars:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch calendars. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle linking a calendar to the accommodation
  const handleLinkCalendar = async (hostGoogleAccountId: string, googleCalendarId: string) => {
    if (!hasPermission || !accommodation) return;

    setIsLoading(true);
    try {
      const { error } = await linkCalendarToAccommodation(
        accommodation.id,
        hostGoogleAccountId,
        googleCalendarId
      );

      if (error) {
        throw new Error(error.message || 'Failed to link calendar');
      }

      toast({
        title: 'Calendar Linked',
        description: 'Google Calendar has been successfully linked to this accommodation.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Refresh accommodation data
      await initializeComponent();
    } catch (error) {
      console.error('Error linking calendar:', error);
      toast({
        title: 'Link Error',
        description: 'Failed to link calendar. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle disconnecting calendar
  const handleDisconnectCalendar = async () => {
    if (!hasPermission || !accommodation) return;

    setIsLoading(true);
    try {
      const { error } = await disconnectCalendarFromAccommodation(accommodation.id);

      if (error) {
        throw new Error(error.message || 'Failed to disconnect calendar');
      }

      toast({
        title: 'Calendar Disconnected',
        description: 'Google Calendar has been successfully disconnected.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Refresh accommodation data
      await initializeComponent();
    } catch (error) {
      console.error('Error disconnecting calendar:', error);
      toast({
        title: 'Disconnect Error',
        description: 'Failed to disconnect calendar. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render if user doesn't have permission
  if (!hasPermission) {
    return (
      <Box p={4} bg="red.50" borderRadius="md">
        <Text color="red.600">
          You do not have permission to manage Google Calendar for this accommodation.
        </Text>
      </Box>
    );
  }

  // Show loading state
  if (isLoading && !accommodation) {
    return (
      <Box p={4} textAlign="center">
        <Text>Loading Google Calendar settings...</Text>
      </Box>
    );
  }

  // Render the component based on current state
  return (
    <Box p={4} borderWidth={1} borderRadius="md">
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        Google Calendar Integration
      </Text>

      {/* Show current status */}
      {accommodation?.is_google_calendar_sync_enabled ? (
        <Box mb={4} p={3} bg="green.50" borderRadius="md">
          <Text color="green.700" fontWeight="medium">
            ✅ Google Calendar is connected and syncing
          </Text>
          <Button
            mt={2}
            size="sm"
            colorScheme="red"
            onClick={handleDisconnectCalendar}
            isLoading={isLoading}
          >
            Disconnect Calendar
          </Button>
        </Box>
      ) : (
        <Box mb={4} p={3} bg="yellow.50" borderRadius="md">
          <Text color="yellow.700" fontWeight="medium">
            ⚠️ Google Calendar is not connected
          </Text>
        </Box>
      )}

      {/* Connection controls */}
      {!accommodation?.is_google_calendar_sync_enabled && (
        <>
          <Button
            colorScheme="blue"
            onClick={handleStartConnection}
            isLoading={isLoading}
            mb={4}
          >
            Connect Google Account
          </Button>

          {/* Show connected accounts if any */}
          {connectedAccounts.length > 0 && (
            <Box>
              <Text fontWeight="medium" mb={2}>Connected Accounts:</Text>
              {connectedAccounts.map((account) => (
                <Box key={account.id} p={2} bg="gray.50" borderRadius="md" mb={2}>
                  <Text fontSize="sm">{account.account_email}</Text>
                  <Button
                    size="sm"
                    mt={1}
                    onClick={() => handleFetchCalendars(account.id)}
                    isLoading={isLoading}
                  >
                    Select Calendar
                  </Button>
                </Box>
              ))}
            </Box>
          )}

          {/* Show calendars if loaded */}
          {calendars.length > 0 && (
            <Box mt={4}>
              <Text fontWeight="medium" mb={2}>Available Calendars:</Text>
              {calendars.map((calendar) => (
                <Box key={calendar.id} p={2} bg="blue.50" borderRadius="md" mb={2}>
                  <Text fontSize="sm" fontWeight="medium">{calendar.summary}</Text>
                  <Button
                    size="sm"
                    mt={1}
                    colorScheme="green"
                    onClick={() => {
                      const selectedAccount = connectedAccounts[0]; // In real usage, track which account was selected
                      if (selectedAccount) {
                        handleLinkCalendar(selectedAccount.id, calendar.id);
                      }
                    }}
                    isLoading={isLoading}
                  >
                    Link This Calendar
                  </Button>
                </Box>
              ))}
            </Box>
          )}
        </>
      )}
    </Box>
  );
};
