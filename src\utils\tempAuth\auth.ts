import { users } from "@/tempData/users";

export const authenticateUser = (username: string, password: string) => {
  const user = users.find(
    (u) => u.username === username && u.password === password
  );
  console.log(user);
  if (user) {
    localStorage.setItem("currentUser", JSON.stringify(user));
    return user;
  }
  return null;
};

export const getCurrentUser = () => {
  if (typeof window === "undefined") return null; // For SSR compatibility

  const userData = localStorage.getItem("currentUser");
  return userData ? JSON.parse(userData) : null;
};

export const logoutUser = () => {
  localStorage.removeItem("currentUser");
};

export const isLoggedIn = () => {
  return !!getCurrentUser();
};
