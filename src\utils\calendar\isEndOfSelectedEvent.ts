import { ReservationType } from "@/types/ReservationType";

type prop = {
  day: Date;
  selectedReservation?: ReservationType;
};

export const isEndOfSelectedEvent = ({ day, selectedReservation }: prop) => {
  if (!selectedReservation) return false;
  const eventEnd = new Date(selectedReservation.end);
  return (
    day.getFullYear() === eventEnd.getFullYear() &&
    day.getMonth() === eventEnd.getMonth() &&
    day.getDate() === eventEnd.getDate()
  );
};
