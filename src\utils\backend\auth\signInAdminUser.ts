import { supabase } from "@/utils/backend/supabase";

export async function signInAdminUser(email: string, password: string) {
  // 1. Authenticate with Supabase Auth
  const { data: authData, error: authError } =
    await supabase.auth.signInWithPassword({
      email,
      password,
    });

  if (authError) {
    return { user: null, error: { type: "auth", message: authError.message } };
  }

  // 2. Fetch user profile from 'users' table to check the role
  const { data: userProfile, error: profileError } = await supabase
    .from("users")
    .select("id, full_name, user_role, avatar_url") // Select necessary profile data
    .eq("id", authData.user.id)
    .single();

  if (profileError) {
    await supabase.auth.signOut(); // Sign out to clear the invalid session
    return {
      user: null,
      error: { type: "server", message: "Failed to retrieve user profile." },
    };
  }

  // 3. Verify the user role
  const allowedRoles = ["admin", "host"];
  if (!allowedRoles.includes(userProfile.user_role)) {
    await supabase.auth.signOut(); // Sign out the unauthorized user
    return {
      user: null,
      error: {
        type: "permission",
        message: "You do not have permission to access this service.",
      },
    };
  }

  // 4. On success, return the user profile data
  return { user: userProfile, error: null };
}
