import { ReservationType } from "@/types/ReservationType";

export const sampleReservations: ReservationType[] = [
  {
    id: "res-001",
    start: new Date("2025-09-05"),
    end: new Date("2025-09-08"),
    createdAt: new Date("2025-07-10"),
    updatedAt: new Date("2025-07-10"),

    data: {
      reserved: {
        userId: "1",
        guestHouseId: "gh-001",
        totalPrice: 45000,
        numberOfGuests: 3,
        checkInTime: "14:00",
        checkOutTime: "11:00",
        specialRequests: "静かな部屋をお願いします",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-002",
    start: new Date("2025-09-15"),
    end: new Date("2025-09-18"),
    createdAt: new Date("2025-08-20"),
    updatedAt: new Date("2025-08-20"),

    data: {
      reserved: {
        userId: "1",
        guestHouseId: "gh-002",
        totalPrice: 25500,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "10:00",
        specialRequests: "朝食不要です",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-003",
    start: new Date("2025-09-12"),
    end: new Date("2025-09-15"),
    createdAt: new Date("2025-07-17"),
    updatedAt: new Date("2025-07-17"),

    data: {
      reserved: {
        userId: "2",
        guestHouseId: "gh-001",
        totalPrice: 45000,
        numberOfGuests: 3,
        checkInTime: "16:00",
        checkOutTime: "12:00",
        specialRequests: "エクストラベッド1台お願いします",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-004",
    start: new Date("2025-10-01"),
    end: new Date("2025-10-04"),
    createdAt: new Date("2025-09-05"),
    updatedAt: new Date("2025-09-05"),

    data: {
      reserved: {
        userId: "2",
        guestHouseId: "gh-003",
        totalPrice: 66000,
        numberOfGuests: 2,
        checkInTime: "13:00",
        checkOutTime: "11:00",
        specialRequests: "海側の部屋希望",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-005",
    start: new Date("2025-09-20"),
    end: new Date("2025-09-25"),
    createdAt: new Date("2025-07-25"),
    updatedAt: new Date("2025-07-25"),

    data: {
      reserved: {
        userId: "3",
        guestHouseId: "gh-001",
        totalPrice: 75000,
        numberOfGuests: 4,
        checkInTime: "15:00",
        checkOutTime: "12:00",
        specialRequests:
          "幼い子供が2人います。可能であればベビーベッドをご用意ください",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-006",
    start: new Date("2025-11-10"),
    end: new Date("2025-11-13"),
    createdAt: new Date("2025-10-15"),
    updatedAt: new Date("2025-10-15"),

    data: {
      reserved: {
        userId: "3",
        guestHouseId: "gh-004",
        totalPrice: 55500,
        numberOfGuests: 2,
        checkInTime: "14:00",
        checkOutTime: "10:00",
        specialRequests: "紅葉狩りを楽しみたいです",
        status: "cancelled",
      },
    },
  },
  {
    id: "res-007",
    start: new Date("2025-08-08"),
    end: new Date("2025-08-10"),
    createdAt: new Date("2025-07-13"),
    updatedAt: new Date("2025-07-13"),

    data: {
      reserved: {
        userId: "4",
        guestHouseId: "gh-002",
        totalPrice: 17000,
        numberOfGuests: 2,
        checkInTime: "17:00",
        checkOutTime: "11:00",
        specialRequests: "夜遅くの到着になります",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-008",
    start: new Date("2025-09-22"),
    end: new Date("2025-09-24"),
    createdAt: new Date("2025-08-27"),
    updatedAt: new Date("2025-08-27"),

    data: {
      reserved: {
        userId: "4",
        guestHouseId: "gh-005",
        totalPrice: 19000,
        numberOfGuests: 1,
        checkInTime: "15:00",
        checkOutTime: "10:00",
        specialRequests: "ベジタリアン対応の朝食をお願いします",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-009",
    start: new Date("2025-08-25"),
    end: new Date("2025-08-29"),
    createdAt: new Date("2025-07-30"),
    updatedAt: new Date("2025-07-30"),

    data: {
      reserved: {
        userId: "5",
        guestHouseId: "gh-003",
        totalPrice: 88000,
        numberOfGuests: 4,
        checkInTime: "14:00",
        checkOutTime: "11:00",
        specialRequests: "子供用のビーチ玩具があれば",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-010",
    start: new Date("2025-10-20"),
    end: new Date("2025-10-22"),
    createdAt: new Date("2025-09-25"),
    updatedAt: new Date("2025-09-25"),

    data: {
      reserved: {
        userId: "5",
        guestHouseId: "gh-006",
        totalPrice: 56000,
        numberOfGuests: 2,
        checkInTime: "16:00",
        checkOutTime: "10:00",
        specialRequests: "竹林の写真撮影をしたいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-011",
    start: new Date("2025-08-01"),
    end: new Date("2025-08-03"),
    createdAt: new Date("2025-07-01"),
    updatedAt: new Date("2025-07-01"),

    data: {
      reserved: {
        userId: "6",
        guestHouseId: "gh-002",
        totalPrice: 24000,
        numberOfGuests: 2,
        checkInTime: "16:00",
        checkOutTime: "11:00",
        specialRequests: "夜8時頃の遅いチェックインになります",
        status: "cancelled",
      },
    },
  },
  {
    id: "res-012",
    start: new Date("2025-11-05"),
    end: new Date("2025-11-08"),
    createdAt: new Date("2025-10-10"),
    updatedAt: new Date("2025-10-10"),

    data: {
      reserved: {
        userId: "6",
        guestHouseId: "gh-004",
        totalPrice: 55500,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "11:00",
        specialRequests: "紅葉の時期なので景色の良い部屋希望",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-013",
    start: new Date("2025-09-16"),
    end: new Date("2025-09-18"),
    createdAt: new Date("2025-07-20"),
    updatedAt: new Date("2025-07-20"),

    data: {
      reserved: {
        userId: "7",
        guestHouseId: "gh-001",
        totalPrice: 30000,
        numberOfGuests: 2,
        checkInTime: "14:00",
        checkOutTime: "11:00",
        specialRequests: "記念日なので部屋を少し飾ってください",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-014",
    start: new Date("2025-10-05"),
    end: new Date("2025-10-08"),
    createdAt: new Date("2025-09-10"),
    updatedAt: new Date("2025-09-10"),

    data: {
      reserved: {
        userId: "7",
        guestHouseId: "gh-005",
        totalPrice: 28500,
        numberOfGuests: 2,
        checkInTime: "13:00",
        checkOutTime: "10:00",
        specialRequests: "奈良公園近くの鹿と会える時間帯を教えてください",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-015",
    start: new Date("2025-08-18"),
    end: new Date("2025-08-21"),
    createdAt: new Date("2025-07-23"),
    updatedAt: new Date("2025-07-23"),

    data: {
      reserved: {
        userId: "8",
        guestHouseId: "gh-003",
        totalPrice: 66000,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "11:00",
        specialRequests: "サーフィン初心者なのでレッスン情報が欲しい",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-016",
    start: new Date("2025-11-15"),
    end: new Date("2025-11-18"),
    createdAt: new Date("2025-10-20"),
    updatedAt: new Date("2025-10-20"),

    data: {
      reserved: {
        userId: "8",
        guestHouseId: "gh-006",
        totalPrice: 84000,
        numberOfGuests: 3,
        checkInTime: "14:00",
        checkOutTime: "10:00",
        specialRequests: "茶道体験を予約したいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-017",
    start: new Date("2025-09-10"),
    end: new Date("2025-09-15"),
    createdAt: new Date("2025-08-15"),
    updatedAt: new Date("2025-09-16"),

    data: {
      reserved: {
        userId: "9",
        guestHouseId: "gh-004",
        totalPrice: 92500,
        numberOfGuests: 2,
        checkInTime: "14:00",
        checkOutTime: "10:00",
        specialRequests: "記念日のお祝いです。お花のご準備をお願いします",
        status: "completed",
      },
    },
  },
  {
    id: "res-018",
    start: new Date("2025-12-01"),
    end: new Date("2025-12-03"),
    createdAt: new Date("2025-11-05"),
    updatedAt: new Date("2025-11-05"),

    data: {
      reserved: {
        userId: "9",
        guestHouseId: "gh-001",
        totalPrice: 45000,
        numberOfGuests: 3,
        checkInTime: "16:00",
        checkOutTime: "11:00",
        specialRequests: "ビジネス出張なので静かな環境でお願いします",
        status: "cancelled",
      },
    },
  },
  {
    id: "res-019",
    start: new Date("2025-08-22"),
    end: new Date("2025-08-25"),
    createdAt: new Date("2025-07-27"),
    updatedAt: new Date("2025-07-27"),

    data: {
      reserved: {
        userId: "10",
        guestHouseId: "gh-002",
        totalPrice: 25500,
        numberOfGuests: 3,
        checkInTime: "15:00",
        checkOutTime: "11:00",
        specialRequests: "大阪観光のおすすめスポットを教えてください",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-020",
    start: new Date("2025-10-12"),
    end: new Date("2025-10-14"),
    createdAt: new Date("2025-09-17"),
    updatedAt: new Date("2025-09-17"),

    data: {
      reserved: {
        userId: "10",
        guestHouseId: "gh-005",
        totalPrice: 19000,
        numberOfGuests: 1,
        checkInTime: "14:00",
        checkOutTime: "10:00",
        specialRequests: "朝食は和食でお願いします",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-021",
    start: new Date("2025-09-01"),
    end: new Date("2025-09-04"),
    createdAt: new Date("2025-08-06"),
    updatedAt: new Date("2025-08-06"),

    data: {
      reserved: {
        userId: "11",
        guestHouseId: "gh-003",
        totalPrice: 66000,
        numberOfGuests: 2,
        checkInTime: "13:00",
        checkOutTime: "11:00",
        specialRequests: "ビーチフロントの部屋希望",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-022",
    start: new Date("2025-11-20"),
    end: new Date("2025-11-22"),
    createdAt: new Date("2025-10-25"),
    updatedAt: new Date("2025-10-25"),

    data: {
      reserved: {
        userId: "11",
        guestHouseId: "gh-006",
        totalPrice: 56000,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "10:00",
        specialRequests: "京都の伝統文化体験をしたいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-023",
    start: new Date("2025-08-28"),
    end: new Date("2025-09-01"),
    createdAt: new Date("2025-08-03"),
    updatedAt: new Date("2025-08-03"),

    data: {
      reserved: {
        userId: "12",
        guestHouseId: "gh-005",
        totalPrice: 38000,
        numberOfGuests: 4,
        checkInTime: "13:00",
        checkOutTime: "11:00",
        specialRequests: "家族でのお盆休みを過ごします",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-024",
    start: new Date("2025-12-15"),
    end: new Date("2025-12-17"),
    createdAt: new Date("2025-11-20"),
    updatedAt: new Date("2025-11-20"),

    data: {
      reserved: {
        userId: "12",
        guestHouseId: "gh-001",
        totalPrice: 45000,
        numberOfGuests: 3,
        checkInTime: "14:00",
        checkOutTime: "11:00",
        specialRequests: "クリスマスイルミネーションを見たいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-025",
    start: new Date("2025-09-05"),
    end: new Date("2025-09-07"),
    createdAt: new Date("2025-08-10"),
    updatedAt: new Date("2025-08-10"),

    data: {
      reserved: {
        userId: "13",
        guestHouseId: "gh-002",
        totalPrice: 17000,
        numberOfGuests: 2,
        checkInTime: "16:00",
        checkOutTime: "11:00",
        specialRequests: "グルメツアーの情報が欲しいです",
        status: "cancelled",
      },
    },
  },
  {
    id: "res-026",
    start: new Date("2025-11-25"),
    end: new Date("2025-11-28"),
    createdAt: new Date("2025-10-30"),
    updatedAt: new Date("2025-10-30"),

    data: {
      reserved: {
        userId: "13",
        guestHouseId: "gh-004",
        totalPrice: 55500,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "10:00",
        specialRequests: "軽井沢の自然を満喫したいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-027",
    start: new Date("2025-08-10"),
    end: new Date("2025-08-13"),
    createdAt: new Date("2025-07-15"),
    updatedAt: new Date("2025-07-15"),

    data: {
      reserved: {
        userId: "14",
        guestHouseId: "gh-003",
        totalPrice: 66000,
        numberOfGuests: 2,
        checkInTime: "14:00",
        checkOutTime: "11:00",
        specialRequests: "カップルでバカンスを楽しみます",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-028",
    start: new Date("2025-10-25"),
    end: new Date("2025-10-27"),
    createdAt: new Date("2025-09-30"),
    updatedAt: new Date("2025-09-30"),

    data: {
      reserved: {
        userId: "14",
        guestHouseId: "gh-006",
        totalPrice: 56000,
        numberOfGuests: 2,
        checkInTime: "16:00",
        checkOutTime: "10:00",
        specialRequests: "京都の夜景を楽しみたいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-029",
    start: new Date("2025-08-07"),
    end: new Date("2025-08-09"),
    createdAt: new Date("2025-07-12"),
    updatedAt: new Date("2025-07-12"),

    data: {
      reserved: {
        userId: "15",
        guestHouseId: "gh-005",
        totalPrice: 19000,
        numberOfGuests: 1,
        checkInTime: "13:00",
        checkOutTime: "11:00",
        specialRequests: "一人旅なので静かな環境でお願いします",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-030",
    start: new Date("2025-10-10"),
    end: new Date("2025-10-18"),
    createdAt: new Date("2025-11-15"),
    updatedAt: new Date("2025-11-15"),

    data: {
      reserved: {
        userId: "15",
        guestHouseId: "gh-001",
        totalPrice: 120000,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "11:00",
        specialRequests: "年末年始の長期滞在です",
        status: "cancelled",
      },
    },
  },
  {
    id: "res-031",
    start: new Date("2025-09-12"),
    end: new Date("2025-09-14"),
    createdAt: new Date("2025-08-17"),
    updatedAt: new Date("2025-08-17"),

    data: {
      reserved: {
        userId: "2",
        guestHouseId: "gh-007",
        totalPrice: 36000,
        numberOfGuests: 2,
        checkInTime: "14:00",
        checkOutTime: "11:00",
        specialRequests: "スキー場までの送迎をお願いします",
        status: "pending",
      },
    },
  },
  {
    id: "res-032",
    start: new Date("2025-11-05"),
    end: new Date("2025-11-07"),
    createdAt: new Date("2025-10-10"),
    updatedAt: new Date("2025-10-10"),

    data: {
      reserved: {
        userId: "5",
        guestHouseId: "gh-008",
        totalPrice: 44000,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "10:00",
        specialRequests: "富士山の写真が撮れる部屋希望",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-033",
    start: new Date("2025-10-20"),
    end: new Date("2025-10-22"),
    createdAt: new Date("2025-09-25"),
    updatedAt: new Date("2025-09-25"),

    data: {
      reserved: {
        userId: "8",
        guestHouseId: "gh-009",
        totalPrice: 33000,
        numberOfGuests: 3,
        checkInTime: "16:00",
        checkOutTime: "11:00",
        specialRequests: "ビジネス出張なので高速WiFi必須",
        status: "cancelled",
      },
    },
  },
  {
    id: "res-034",
    start: new Date("2025-12-05"),
    end: new Date("2025-12-07"),
    createdAt: new Date("2025-11-10"),
    updatedAt: new Date("2025-11-10"),

    data: {
      reserved: {
        userId: "11",
        guestHouseId: "gh-010",
        totalPrice: 48000,
        numberOfGuests: 2,
        checkInTime: "14:00",
        checkOutTime: "10:00",
        specialRequests: "神戸の夜景を楽しみたいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-035",
    start: new Date("2025-08-30"),
    end: new Date("2025-09-01"),
    createdAt: new Date("2025-08-05"),
    updatedAt: new Date("2025-08-05"),

    data: {
      reserved: {
        userId: "14",
        guestHouseId: "gh-011",
        totalPrice: 27000,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "11:00",
        specialRequests: "茶道体験を予約したいです",
        status: "cancelled",
      },
    },
  },
  {
    id: "res-036",
    start: new Date("2025-10-15"),
    end: new Date("2025-10-17"),
    createdAt: new Date("2025-09-20"),
    updatedAt: new Date("2025-09-20"),

    data: {
      reserved: {
        userId: "3",
        guestHouseId: "gh-012",
        totalPrice: 58500,
        numberOfGuests: 4,
        checkInTime: "14:00",
        checkOutTime: "11:00",
        specialRequests: "ビーチアクティビティを楽しみたいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-037",
    start: new Date("2025-11-10"),
    end: new Date("2025-11-12"),
    createdAt: new Date("2025-10-15"),
    updatedAt: new Date("2025-10-15"),

    data: {
      reserved: {
        userId: "6",
        guestHouseId: "gh-013",
        totalPrice: 37500,
        numberOfGuests: 3,
        checkInTime: "15:00",
        checkOutTime: "10:00",
        specialRequests: "高山祭りの時期に合わせて予約しました",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-038",
    start: new Date("2025-12-20"),
    end: new Date("2025-12-22"),
    createdAt: new Date("2025-11-25"),
    updatedAt: new Date("2025-11-25"),

    data: {
      reserved: {
        userId: "9",
        guestHouseId: "gh-014",
        totalPrice: 43500,
        numberOfGuests: 3,
        checkInTime: "16:00",
        checkOutTime: "11:00",
        specialRequests: "クリスマスイルミネーションを見に来ました",
        status: "pending",
      },
    },
  },
  {
    id: "res-039",
    start: new Date("2025-09-25"),
    end: new Date("2025-09-27"),
    createdAt: new Date("2025-08-30"),
    updatedAt: new Date("2025-08-30"),

    data: {
      reserved: {
        userId: "12",
        guestHouseId: "gh-015",
        totalPrice: 52500,
        numberOfGuests: 3,
        checkInTime: "14:00",
        checkOutTime: "11:00",
        specialRequests: "ハイキングを楽しみたいです",
        status: "confirmed",
      },
    },
  },
  {
    id: "res-040",
    start: new Date("2025-11-30"),
    end: new Date("2025-12-02"),
    createdAt: new Date("2025-11-05"),
    updatedAt: new Date("2025-11-05"),

    data: {
      reserved: {
        userId: "15",
        guestHouseId: "gh-016",
        totalPrice: 37000,
        numberOfGuests: 2,
        checkInTime: "15:00",
        checkOutTime: "10:00",
        specialRequests: "京都でアートを楽しみたいです",
        status: "confirmed",
      },
    },
  },
];
