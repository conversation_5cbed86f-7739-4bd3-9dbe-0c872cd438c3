import ClockIcon from "@/assets/icons/ClockIcon";
import { Flex, Grid, Image, Link, Text } from "@chakra-ui/react";
import React from "react";
import { UserType } from "@/types/UserType";
import { MessagesType } from "@/types/MessagesType";
import { users } from "@/tempData/users";
import { sampleConversations } from "@/tempData/chat";

type props = {
  currentUser: UserType;
  message: MessagesType;
};

export const Message = ({ message, currentUser }: props) => {
  const sender = users.find(
    (item) => item.id === message.participants[parseInt(currentUser.id)]
  );

  const lastMessage = sampleConversations.find(
    (item) => item.id === message.lastMessage
  );

  return (
    <Link
      href={`/user/message/${message.id}?sender=${sender?.id}`}
      textDecoration={"none"}
    >
      <Grid
        templateColumns={"5rem 1fr 7rem"}
        borderBottom={"1px solid"}
        borderColor={"gray.200"}
        py={".8rem"}
        px={".5rem"}
        gap={"1rem"}
      >
        <Flex
          overflow={"hidden"}
          alignItems={"center"}
          justifyContent={"center"}
          objectFit={"cover"}
          borderRadius={"1rem"}
          w={"5rem"}
          h={"5rem"}
        >
          <Image
            src={sender && sender.image}
            alt="Tokyo Guesthouse"
            w={"auto"}
            h={"5rem"}
          />
        </Flex>
        <Flex flexDirection={"column"} gap={".4rem"}>
          <Text fontWeight={"bold"} fontSize={"1.2rem"}>
            {sender && sender.name}
          </Text>
          <Text fontSize={".7rem"} color={"gray.500"}>
            {sender && sender.address}
          </Text>
          <Text fontSize={".9rem"} noOfLines={1} w={"90%"} overflow={"hidden"}>
            {/* {chat && chat.content} */}
            {lastMessage && lastMessage.content}
          </Text>
        </Flex>
        <Grid gridTemplateRows={"1fr 1fr"}>
          <Flex justifyContent={"flex-end"} alignItems={"center"} gap={".3rem"}>
            <ClockIcon />
            <Text fontSize={".8rem"} color={"gray.500"}>
              {/* {chat && chat.time} */}
              {lastMessage &&
                lastMessage.timestamp.toLocaleString("en-US", {
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: true,
                })}
            </Text>
          </Flex>
          <Flex justifyContent={"flex-end"} alignItems={"center"}>
            {/* <Flex bg={"red"} borderRadius={"full"} p={".3rem"}></Flex> */}
            {lastMessage && lastMessage.viewed ? null : (
              <Flex bg={"red"} borderRadius={"full"} p={".3rem"}></Flex>
            )}
          </Flex>
        </Grid>
      </Grid>
    </Link>
  );
};
