/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  async rewrites() {
    return [
      {
        // Requests to '/functions/...'
        source: "/functions/:path*",
        // will be forwarded to the Supabase URL defined in your environment variables.
        destination: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
