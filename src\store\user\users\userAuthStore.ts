import { UserType } from "@/types/UserType";
import { create } from "zustand";
import { persist } from "zustand/middleware";

type UserState = {
  currentUser: UserType | null;
  setCurrentUser: (user: UserType | null) => void;
  clearUser: () => void;
};

export const useUserAuthStore = create<UserState>()(
  persist(
    (set) => ({
      currentUser: null,

      setCurrentUser: (user) => set({ currentUser: user }),

      clearUser: () => set({ currentUser: null }),
    }),
    {
      name: "currentUser",
      storage: {
        getItem: (name) => {
          if (typeof window === "undefined") return null;
          const item = localStorage.getItem(name);
          return item ? JSON.parse(item) : null;
        },
        setItem: (name, value) => {
          if (typeof window !== "undefined") {
            localStorage.setItem(name, JSON.stringify(value));
          }
        },
        removeItem: (name) => {
          if (typeof window !== "undefined") {
            localStorage.removeItem(name);
          }
        },
      },
    }
  )
);

export const getCurrentUser = () => {
  if (typeof window === "undefined") return null;
  return useUserAuthStore.getState().currentUser;
};
