# Google Calendar Integration Guide

## Overview

The Google Calendar integration allows hosts to connect their Google Calendar to their accommodations for two-way synchronization. This feature enables automatic blocking of dates when bookings are made and helps prevent double bookings.

## Implementation Status

✅ **COMPLETED**: The Google Calendar integration has been fully implemented in the accommodation detail page.

## Features

- **OAuth Authentication**: Secure Google account connection using OAuth 2.0
- **Calendar Selection**: Choose specific calendars from connected Google accounts
- **Two-way Sync**: Synchronize bookings between the platform and Google Calendar
- **Multiple Account Support**: Connect multiple Google accounts (though only one calendar per accommodation)
- **Disconnect Functionality**: Easy disconnection and reconnection of calendars

## Usage

### For Hosts

1. **Navigate to Accommodation Management**
   - Go to `/user/management/{accommodation_id}`
   - You'll see the Google Calendar integration card

2. **Connect Google Account**
   - Click the "連動" (Connect) button
   - You'll be redirected to Google's OAuth consent screen
   - Grant the necessary calendar permissions
   - You'll be redirected back to the accommodation page

3. **Select Calendar**
   - After connecting, you'll see your connected accounts
   - Click "Select Calendar" for the account you want to use
   - Choose the specific calendar from the modal
   - The calendar will be linked to your accommodation

4. **Manage Connection**
   - Once connected, you'll see the connected account email
   - Use the "連動解除" (Disconnect) button to remove the connection
   - You can reconnect at any time

### Current Implementation

The integration is implemented in:
- **Main Page**: `src/pages/user/management/[res-id]/index.tsx`
- **Component**: `src/components/management/GoogleCalendar.tsx`
- **API Utilities**: `src/utils/backend/googleCalendar/googleCalendarAPI.ts`

## Technical Details

### API Functions

The following functions are available for Google Calendar operations:

```typescript
// Start OAuth connection
startGoogleConnection(currentPagePath: string): void

// Get connected accounts
getConnectedGoogleAccounts(): Promise<GoogleAccount[]>

// Fetch calendars for an account
fetchCalendarList(hostGoogleAccountId: string): Promise<Calendar[] | null>

// Link calendar to accommodation
linkCalendarToAccommodation(
  accommodationId: string, 
  hostGoogleAccountId: string, 
  googleCalendarId: string
): Promise<{ error: any }>

// Get accommodation details
getAccommodationDetails(accommodationId: string): Promise<{
  accommodation: AccommodationDetails | null;
  error: any;
}>

// Disconnect calendar
disconnectCalendarFromAccommodation(accommodationId: string): Promise<{ error: any }>
```

### Database Schema

The integration uses the following database tables:
- `accommodations`: Stores sync status and linked calendar information
- `host_google_accounts`: Stores connected Google account information

### Security

- **Permission-based**: Only accommodation owners and admins can manage calendar connections
- **OAuth 2.0**: Secure authentication using Google's OAuth system
- **Token Management**: Handled server-side through Supabase Edge Functions

## Error Handling

The implementation includes comprehensive error handling for:
- Network connectivity issues
- Authentication failures
- Missing or unavailable Edge Functions
- Permission errors
- API rate limiting

## Backend Dependencies

The integration relies on the following Supabase Edge Functions:
- `google-oauth-start`: Initiates the OAuth flow
- `get-google-calendars`: Fetches calendar lists

**Note**: If you encounter errors related to these functions not being available, please contact the backend team immediately as specified in the original documentation.

## UI States

The Google Calendar component handles multiple states:
- **Loading**: Shows spinner during API calls
- **Not Connected**: Shows connect button
- **Connected**: Shows connected account and disconnect option
- **Calendar Selection**: Modal for choosing specific calendar
- **Error States**: Toast notifications for various error conditions

## Future Enhancements

Potential improvements could include:
- Bulk calendar operations
- Calendar event preview
- Sync status indicators
- Advanced sync settings
- Multiple calendar support per accommodation
