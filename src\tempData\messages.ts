import { MessagesType } from "@/types/MessagesType";

export const sampleMessages: MessagesType[] = [
  {
    id: "conv-1",
    participants: ["1", "2"],
    messages: ["msg-1", "msg-2", "msg-3", "msg-4"],
    lastMessage: "msg-4",
    unreadCount: 0,
  },
  {
    id: "conv-2",
    participants: ["1", "3"],
    messages: ["msg-5", "msg-6", "msg-7"],
    lastMessage: "msg-7",
    unreadCount: 2,
  },
  {
    id: "conv-3",
    participants: ["1", "4"],
    messages: ["msg-8", "msg-9", "msg-10", "msg-11"],
    lastMessage: "msg-11",
    unreadCount: 0,
  },
  {
    id: "conv-4",
    participants: ["1", "5"],
    messages: ["msg-12"],
    lastMessage: "msg-12",
    unreadCount: 1,
  },
];
