import moment from "moment";

export interface UserEvent {
  id: number;
  userID: number;
  start: Date;
  end: Date;
  type: "reservation" | "blackout";
  data: {
    name: string;
    reservationNumber?: string;
    fee?: number;
    maxGuests?: number;
    checkIn?: string;
    checkOut?: string;
    requests?: string;
  };
}

export const userEvents: UserEvent[] = [
  {
    id: 1,
    userID: 1,
    start: moment("2025-08-05T15:00:00").toDate(),
    end: moment("2025-08-07T12:00:00").toDate(),
    type: "reservation",
    data: {
      name: "山田太郎の予約",
      reservationNumber: "1234567890",
      fee: 123000,
      maxGuests: 3,
      checkIn: "15:00",
      checkOut: "12:00",
      requests: "静かな部屋を希望",
    },
  },
  {
    id: 2,
    userID: 2,
    start: moment("2025-08-10T15:00:00").toDate(),
    end: moment("2025-08-12T12:00:00").toDate(),
    type: "reservation",
    data: {
      name: "佐藤花子の予約",
      reservationNumber: "1234567891",
      fee: 98000,
      maxGuests: 2,
      checkIn: "15:00",
      checkOut: "12:00",
      requests: "禁煙室希望",
    },
  },
  {
    id: 3,
    userID: 1,
    start: moment("2025-08-15T15:00:00").toDate(),
    end: moment("2025-08-17T12:00:00").toDate(),
    type: "reservation",
    data: {
      name: "山田太郎の予約",
      reservationNumber: "1234567892",
      fee: 156000,
      maxGuests: 4,
      checkIn: "15:00",
      checkOut: "12:00",
      requests: "ファミリールーム希望",
    },
  },
  {
    id: 4,
    userID: 3,
    start: moment("2025-08-20T15:00:00").toDate(),
    end: moment("2025-08-22T12:00:00").toDate(),
    type: "reservation",
    data: {
      name: "鈴木一郎の予約",
      reservationNumber: "1234567893",
      fee: 87000,
      maxGuests: 1,
      checkIn: "15:00",
      checkOut: "12:00",
      requests: "ビジネス利用",
    },
  },
  {
    id: 5,
    userID: 1,
    start: moment("2025-08-25T00:00:00").toDate(),
    end: moment("2025-08-27T23:59:59").toDate(),
    type: "blackout",
    data: {
      name: "メンテナンス期間",
    },
  },
  {
    id: 6,
    userID: 4,
    start: moment("2025-09-01T15:00:00").toDate(),
    end: moment("2025-09-03T12:00:00").toDate(),
    type: "reservation",
    data: {
      name: "田中美咲の予約",
      reservationNumber: "1234567894",
      fee: 145000,
      maxGuests: 2,
      checkIn: "15:00",
      checkOut: "12:00",
      requests: "記念日利用",
    },
  },
  {
    id: 7,
    userID: 2,
    start: moment("2025-09-05T15:00:00").toDate(),
    end: moment("2025-09-07T12:00:00").toDate(),
    type: "reservation",
    data: {
      name: "佐藤花子の予約",
      reservationNumber: "1234567895",
      fee: 112000,
      maxGuests: 3,
      checkIn: "15:00",
      checkOut: "12:00",
      requests: "朝食付きプラン",
    },
  },
];
