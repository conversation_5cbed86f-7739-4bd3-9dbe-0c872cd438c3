/**
 * Debug component to help troubleshoot authentication issues
 * This component shows the current authentication state and can be used
 * to diagnose Google Calendar integration problems
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Text,
  Button,
  VStack,
  HStack,
  Badge,
  Code,
  Divider,
  useToast,
  Collapse,
  useDisclosure
} from '@chakra-ui/react';
import { supabase } from '@/utils/backend/supabase';
import { useUserAuthStore } from '@/store/user/users/userAuthStore';

export const AuthDebugger = () => {
  const [supabaseSession, setSupabaseSession] = useState<any>(null);
  const [supabaseUser, setSupabaseUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { isOpen, onToggle } = useDisclosure();
  const toast = useToast();
  
  // Get Zustand auth state
  const currentUser = useUserAuthStore((state) => state.currentUser);

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    setIsLoading(true);
    try {
      // Check Supabase session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      setSupabaseSession(session);

      // Check Supabase user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      setSupabaseUser(user);

      if (sessionError) {
        console.error('Session error:', sessionError);
      }
      if (userError) {
        console.error('User error:', userError);
      }
    } catch (error) {
      console.error('Auth check error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testGoogleOAuthEndpoint = async () => {
    try {
      const response = await fetch('/api/google-oauth-start?redirect_to=/test', {
        method: 'GET',
        credentials: 'include', // Include cookies
      });

      if (response.ok) {
        toast({
          title: 'Endpoint Test Success',
          description: 'The Google OAuth endpoint is accessible.',
          status: 'success',
          duration: 3000,
        });
      } else {
        const errorText = await response.text();
        toast({
          title: `Endpoint Test Failed (${response.status})`,
          description: errorText,
          status: 'error',
          duration: 5000,
        });
      }
    } catch (error) {
      toast({
        title: 'Endpoint Test Error',
        description: `Network error: ${error}`,
        status: 'error',
        duration: 5000,
      });
    }
  };

  const getAuthStatus = () => {
    if (supabaseSession && supabaseUser) {
      return { status: 'success', text: 'Fully Authenticated' };
    } else if (supabaseUser && !supabaseSession) {
      return { status: 'warning', text: 'User Found, No Session' };
    } else if (!supabaseUser && supabaseSession) {
      return { status: 'warning', text: 'Session Found, No User' };
    } else {
      return { status: 'error', text: 'Not Authenticated' };
    }
  };

  const authStatus = getAuthStatus();

  return (
    <Box p={4} borderWidth={1} borderRadius="md" bg="gray.50">
      <HStack justify="space-between" mb={4}>
        <Text fontSize="lg" fontWeight="bold">
          Authentication Debugger
        </Text>
        <Button size="sm" onClick={onToggle}>
          {isOpen ? 'Hide' : 'Show'} Details
        </Button>
      </HStack>

      <VStack align="stretch" spacing={3}>
        {/* Overall Status */}
        <HStack>
          <Text fontWeight="medium">Status:</Text>
          <Badge colorScheme={authStatus.status === 'success' ? 'green' : authStatus.status === 'warning' ? 'yellow' : 'red'}>
            {authStatus.text}
          </Badge>
        </HStack>

        {/* Quick Actions */}
        <HStack spacing={2}>
          <Button size="sm" onClick={checkAuthState} isLoading={isLoading}>
            Refresh Auth State
          </Button>
          <Button size="sm" onClick={testGoogleOAuthEndpoint} colorScheme="blue">
            Test OAuth Endpoint
          </Button>
        </HStack>

        <Collapse in={isOpen}>
          <VStack align="stretch" spacing={4}>
            <Divider />

            {/* Zustand Store State */}
            <Box>
              <Text fontWeight="medium" mb={2}>Zustand Auth Store:</Text>
              <Code p={2} borderRadius="md" fontSize="sm" display="block" whiteSpace="pre-wrap">
                {JSON.stringify(currentUser, null, 2) || 'null'}
              </Code>
            </Box>

            {/* Supabase Session */}
            <Box>
              <Text fontWeight="medium" mb={2}>Supabase Session:</Text>
              <Code p={2} borderRadius="md" fontSize="sm" display="block" whiteSpace="pre-wrap" maxH="200px" overflowY="auto">
                {supabaseSession ? JSON.stringify({
                  access_token: supabaseSession.access_token ? '***PRESENT***' : null,
                  refresh_token: supabaseSession.refresh_token ? '***PRESENT***' : null,
                  expires_at: supabaseSession.expires_at,
                  expires_in: supabaseSession.expires_in,
                  token_type: supabaseSession.token_type,
                  user: {
                    id: supabaseSession.user?.id,
                    email: supabaseSession.user?.email,
                    role: supabaseSession.user?.role,
                  }
                }, null, 2) : 'null'}
              </Code>
            </Box>

            {/* Supabase User */}
            <Box>
              <Text fontWeight="medium" mb={2}>Supabase User:</Text>
              <Code p={2} borderRadius="md" fontSize="sm" display="block" whiteSpace="pre-wrap" maxH="200px" overflowY="auto">
                {supabaseUser ? JSON.stringify({
                  id: supabaseUser.id,
                  email: supabaseUser.email,
                  role: supabaseUser.role,
                  created_at: supabaseUser.created_at,
                  last_sign_in_at: supabaseUser.last_sign_in_at,
                }, null, 2) : 'null'}
              </Code>
            </Box>

            {/* Environment Check */}
            <Box>
              <Text fontWeight="medium" mb={2}>Environment:</Text>
              <Code p={2} borderRadius="md" fontSize="sm" display="block" whiteSpace="pre-wrap">
                {JSON.stringify({
                  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? '***SET***' : 'NOT SET',
                  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '***SET***' : 'NOT SET',
                  NODE_ENV: process.env.NODE_ENV,
                }, null, 2)}
              </Code>
            </Box>

            {/* Troubleshooting Tips */}
            <Box>
              <Text fontWeight="medium" mb={2}>Troubleshooting Tips:</Text>
              <VStack align="stretch" spacing={1} fontSize="sm">
                <Text>• If "Not Authenticated": User needs to log in through /login</Text>
                <Text>• If "User Found, No Session": Session may have expired, try refreshing</Text>
                <Text>• If OAuth endpoint test fails with 401: Authentication issue confirmed</Text>
                <Text>• If OAuth endpoint test fails with 404: Backend function not deployed</Text>
                <Text>• Check browser console for additional error details</Text>
              </VStack>
            </Box>
          </VStack>
        </Collapse>
      </VStack>
    </Box>
  );
};
