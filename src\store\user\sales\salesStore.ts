import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { OverallOverviewType } from "@/types/sales/OverallOverviewType";
import { getSalesOverview } from "@/utils/sales/getSalesOverview";
import { GrossProfitByGuesthouseType } from "@/types/sales/grossProfitByGuesthouseType";
import { getGrossProfitByGuestHouse } from "@/utils/sales/getRevenueByGuestHouse";

type SalesStore = {
  overallOverview: OverallOverviewType;
  revenueOverviewByGuestHouse: GrossProfitByGuesthouseType[];
};

export const useSalesStore = create<SalesStore>()(
  immer((set) => ({
    overallOverview: getSalesOverview(),
    revenueOverviewByGuestHouse: getGrossProfitByGuestHouse(),
  }))
);
