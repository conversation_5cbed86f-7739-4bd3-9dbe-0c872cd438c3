import React from "react";
import { Flex } from "@chakra-ui/react";
import { Sales } from "./Sales";
import { useSalesStore } from "@/store/user/sales/salesStore";

export const Body = () => {
  const revenueOverviewByGuestHouse = useSalesStore(
    (state) => state.revenueOverviewByGuestHouse
  );

  return (
    <Flex flexDirection={"column"}>
      {revenueOverviewByGuestHouse.map((item) => (
        <Sales key={item.guesthouseName} grossProfitByGuestHouse={item} />
      ))}
    </Flex>
  );
};
