import { Box } from "@chakra-ui/react";
import React, { useEffect } from "react";
import { users } from "@/tempData/users";
import { useRouter } from "next/router";

export const Event = (reservationData: any) => {
  const router = useRouter();
  const data = reservationData.reservationData;
  const [user, setUser] = React.useState<any>(null);
  useEffect(() => {
    const user = users.find((user) => user.id === data.data.reserved.userId);
    setUser(user);
  }, [data]);

  return (
    <Box
      w={"100%"}
      h={"100%"}
      color={"white"}
      borderRadius={"1rem"}
      px={"1rem"}
      onClick={() => {
        router.push(
          `/user/management/${data.data.reserved.guestHouseId}/${data.id}`
        );
      }}
    >
      {user && user.name}
    </Box>
  );
};
