import { Flex, Grid, <PERSON>, Text, Button } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";
import { TextPair } from "@/components/ui/TextPair";
import { useMessagesStore } from "@/store/user/messages/messagesStore";
import { formatAmount } from "@/utils/sales/formatAmount";

const Index = () => {
  const router = useRouter();
  const userID = React.useMemo(() => {
    const id = router.query["message-detail-info-id"];
    if (typeof id !== "string") return undefined;
    return id;
  }, [router.query]);

  const getUserReservation = useMessagesStore(
    (state) => state.getUserReservation
  );
  const userReservation = useMessagesStore((state) => state.userReservation);

  React.useEffect(() => {
    if (typeof userID !== "string") return;
    try {
      return getUserReservation(userID);
    } catch {
      return undefined;
    }
  }, [userID, getUserReservation]);

  return (
    <Flex flexDirection={"column"}>
      <Grid
        gap={"1rem"}
        alignItems={"center"}
        pb={"1rem"}
        borderBottom={"1px solid"}
        borderColor={"gray.200"}
        gridTemplateColumns={"5rem 1fr"}
      >
        <Flex
          overflow={"hidden"}
          alignItems={"center"}
          justifyContent={"center"}
          borderRadius={"1rem"}
          w={"5rem"}
          h={"5rem"}
        >
          <Image
            src={userReservation.user?.image}
            objectFit={"cover"}
            objectPosition={"center"}
            alt="Tokyo Guesthouse"
            w={"auto"}
            h={"5rem"}
          />
        </Flex>
        <Flex flexDirection={"column"} gap={".4rem"}>
          <Text fontWeight={"bold"} fontSize={"1.2rem"}>
            {userReservation.user?.name}
          </Text>
          <Text fontSize={".9rem"} color={"gray.500"}>
            {userReservation.user?.address}
          </Text>
        </Flex>
      </Grid>
      <Flex flexDirection={"column"} py={"1rem"}>
        <Text fontWeight={"bold"} fontSize={"1.1rem"}>
          予約情報
        </Text>
        <Flex flexDirection={"column"} py={"1rem"} gap={"1rem"}>
          <TextPair
            label={"予約番号"}
            value={userReservation.reservation?.id ?? ""}
          />
          <TextPair
            label={"日程"}
            value={
              userReservation.reservation?.start
                ? userReservation.reservation?.start.toLocaleDateString()
                : ""
            }
          />
          <TextPair
            label={"料金"}
            value={formatAmount(
              userReservation.reservation?.data.reserved.totalPrice ?? 0
            )}
          />

          <TextPair
            label={"最大宿泊人数"}
            value={
              userReservation.reservation?.data.reserved.numberOfGuests != null
                ? `${userReservation.reservation.data.reserved.numberOfGuests}名`
                : ""
            }
          />
          <TextPair
            label={"チェックイン"}
            value={userReservation.reservation?.data.reserved.checkInTime ?? ""}
          />
          <TextPair
            label={"チェックイアウト"}
            value={
              userReservation.reservation?.data.reserved.checkOutTime ?? ""
            }
          />
          <TextPair
            label={"要望事項"}
            value={
              userReservation.reservation?.data.reserved.specialRequests ?? ""
            }
          />
        </Flex>
      </Flex>
      <Flex flexDirection={"column"} pt={"1rem"}>
        <Button
          variant={"primary"}
          bg={"gray.100"}
          color={"black"}
          fontWeight={"semibold"}
          onClick={() => {
            router.back();
          }}
        >
          戻る
        </Button>
      </Flex>
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
