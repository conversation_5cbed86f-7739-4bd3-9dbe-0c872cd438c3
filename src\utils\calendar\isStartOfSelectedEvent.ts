import { ReservationType } from "@/types/ReservationType";

type prop = {
  day: Date;
  selectedReservation?: ReservationType;
};

export const isStartOfSelectedEvent = ({ day, selectedReservation }: prop) => {
  if (!selectedReservation) return false;
  const eventStart = new Date(selectedReservation.start);
  return (
    day.getFullYear() === eventStart.getFullYear() &&
    day.getMonth() === eventStart.getMonth() &&
    day.getDate() === eventStart.getDate()
  );
};
