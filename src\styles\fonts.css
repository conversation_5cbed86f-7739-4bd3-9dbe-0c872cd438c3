@import url("https://cdn.jsdelivr.net/gh/orioncactus/pretendard/dist/web/static/pretendard-jp.css");

@font-face {
  font-family: "Pretendard";
  font-weight: 100;
  font-style: normal;
  src: url("/fonts/Pretendard-Thin.otf") format("opentype");
  font-display: swap;
}

@font-face {
  font-family: "Pretendard";
  font-weight: 200;
  font-style: normal;
  src: url("/fonts/Pretendard-ExtraLight.otf") format("opentype");
  font-display: swap;
}

@font-face {
  font-family: "Pretendard";
  font-weight: 300;
  font-style: normal;
  src: url("/fonts/Pretendard-Light.otf") format("opentype");
  font-display: swap;
}

@font-face {
  font-family: "Pretendard";
  font-weight: 400;
  font-style: normal;
  src: url("/fonts/Pretendard-Regular.otf") format("opentype");
  font-display: swap;
}

@font-face {
  font-family: "Pretendard";
  font-weight: 500;
  font-style: normal;
  src: url("/fonts/Pretendard-Medium.otf") format("opentype");
  font-display: swap;
}

@font-face {
  font-family: "Pretendard";
  font-weight: 600;
  font-style: normal;
  src: url("/fonts/Pretendard-SemiBold.otf") format("opentype");
  font-display: swap;
}

@font-face {
  font-family: "Pretendard";
  font-weight: 700;
  font-style: normal;
  src: url("/fonts/Pretendard-Bold.otf") format("opentype");
  font-display: swap;
}

@font-face {
  font-family: "Pretendard";
  font-weight: 800;
  font-style: normal;
  src: url("/fonts/Pretendard-ExtraBold.otf") format("opentype");
  font-display: swap;
}

@font-face {
  font-family: "Pretendard";
  font-weight: 900;
  font-style: normal;
  src: url("/fonts/Pretendard-Black.otf") format("opentype");
  font-display: swap;
}

:root {
  --font-pretendard: "Pretendard", -apple-system, BlinkMacSystemFont, system-ui,
    Roboto, "Helvetica Neue", "Segoe UI", "Apple SD Gothic Neo", "Noto Sans KR",
    "Malgun Gothic", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    sans-serif;
}

html {
  font-family: var(--font-pretendard);
}
