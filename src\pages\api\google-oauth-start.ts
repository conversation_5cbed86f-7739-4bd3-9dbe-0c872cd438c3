import type { NextApiRequest, NextApiResponse } from "next";
import { createPagesServerClient } from "@supabase/auth-helpers-nextjs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const supabase = createPagesServerClient({ req, res });

  const {
    data: { session },
    error,
  } = await supabase.auth.getSession();

  if (!session) {
    return res.status(401).json({ error: "Not logged in" });
  }

  const redirectTo = req.query.redirect_to as string;
  const url = `${
    process.env.NEXT_PUBLIC_SUPABASE_URL
  }/functions/v1/google-oauth-start?redirect_to=${encodeURIComponent(
    redirectTo
  )}`;

  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${session.access_token}`,
    },
  });

  if (response.redirected) {
    return res.redirect(response.url);
  }

  const text = await response.text();
  res.status(response.status).send(text);
}
