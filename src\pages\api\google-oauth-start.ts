import type { NextApiRequest, NextApiResponse } from "next";
import { createPagesServerClient } from "@supabase/auth-helpers-nextjs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const supabase = createPagesServerClient({ req, res });

    // Debug: Log request details
    console.log("🔍 Google OAuth Start API called");
    console.log("📋 Headers:", {
      cookie: req.headers.cookie ? "Present" : "Missing",
      cookieLength: req.headers.cookie?.length || 0,
      userAgent: req.headers["user-agent"],
    });

    // Try to get session first
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    // If no session, try to get user directly (sometimes session is missing but user exists)
    let fallbackUser = null;
    if (!session) {
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();
      fallbackUser = user;
      console.log("🔄 Fallback user check:", {
        hasUser: !!user,
        userError: userError?.message,
      });
    }

    // Debug: Log session details
    console.log("🔐 Session check:", {
      hasSession: !!session,
      hasAccessToken: !!session?.access_token,
      userId: session?.user?.id,
      sessionError: sessionError?.message,
    });

    if (sessionError) {
      console.error("❌ Session error:", sessionError);
      return res.status(401).json({
        error: "Session error",
        details: sessionError.message,
        debug: true,
      });
    }

    if (!session) {
      console.log("❌ No session found");

      // Provide more specific error information
      let errorDetails = "No valid Supabase session found.";
      if (fallbackUser) {
        errorDetails +=
          " User exists but session is missing - try refreshing the page or logging in again.";
      } else {
        errorDetails += " Please log in first.";
      }

      return res.status(401).json({
        error: "Not logged in",
        details: errorDetails,
        debug: true,
        hasCookie: !!req.headers.cookie,
        hasUser: !!fallbackUser,
        suggestions: fallbackUser
          ? [
              "Refresh the page",
              "Log out and log in again",
              "Clear browser storage and log in",
            ]
          : ["Navigate to /login", "Ensure you're logged in with Supabase"],
      });
    }

    if (!session.access_token) {
      console.log("❌ No access token in session");
      return res.status(401).json({
        error: "Invalid session",
        details: "Session exists but no access token found.",
        debug: true,
      });
    }

    const redirectTo = req.query.redirect_to as string;
    if (!redirectTo) {
      return res.status(400).json({
        error: "Missing redirect_to parameter",
        debug: true,
      });
    }

    const url = `${
      process.env.NEXT_PUBLIC_SUPABASE_URL
    }/functions/v1/google-oauth-start?redirect_to=${encodeURIComponent(
      redirectTo
    )}`;

    console.log("🚀 Calling Supabase Edge Function:", url);

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${session.access_token}`,
        "Content-Type": "application/json",
      },
    });

    console.log("📡 Edge Function Response:", {
      status: response.status,
      statusText: response.statusText,
      redirected: response.redirected,
      url: response.url,
    });

    if (response.redirected) {
      console.log("↩️ Redirecting to:", response.url);
      return res.redirect(response.url);
    }

    const text = await response.text();
    console.log("📄 Edge Function Response Body:", text);

    res.status(response.status).send(text);
  } catch (error) {
    console.error("💥 Google OAuth Start API Error:", error);
    return res.status(500).json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error",
      debug: true,
    });
  }
}
