/**
 * Authentication test utilities for debugging Google Calendar integration
 * These functions can be called from the browser console to test auth state
 */

import { supabase } from '@/utils/backend/supabase';

/**
 * Test the current Supabase authentication state
 */
export const testSupabaseAuth = async () => {
  console.log('🔍 Testing Supabase Authentication...');
  
  try {
    // Test session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log('📋 Session:', session ? '✅ Present' : '❌ Missing');
    if (sessionError) {
      console.error('❌ Session Error:', sessionError);
    }

    // Test user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('👤 User:', user ? '✅ Present' : '❌ Missing');
    if (userError) {
      console.error('❌ User Error:', userError);
    }

    // Test token validity
    if (session?.access_token) {
      console.log('🔑 Access Token:', '✅ Present');
      console.log('⏰ Token Expires:', new Date(session.expires_at! * 1000).toLocaleString());
      
      const now = Date.now() / 1000;
      const isExpired = session.expires_at! < now;
      console.log('🕐 Token Status:', isExpired ? '❌ Expired' : '✅ Valid');
    } else {
      console.log('🔑 Access Token:', '❌ Missing');
    }

    return {
      hasSession: !!session,
      hasUser: !!user,
      hasToken: !!session?.access_token,
      isTokenValid: session?.expires_at ? session.expires_at > Date.now() / 1000 : false,
      userId: user?.id,
      userEmail: user?.email,
    };
  } catch (error) {
    console.error('💥 Auth Test Failed:', error);
    return null;
  }
};

/**
 * Test the Google OAuth API endpoint
 */
export const testGoogleOAuthEndpoint = async () => {
  console.log('🔍 Testing Google OAuth Endpoint...');
  
  try {
    const response = await fetch('/api/google-oauth-start?redirect_to=/test', {
      method: 'GET',
      credentials: 'include',
    });

    console.log('📡 Response Status:', response.status);
    console.log('📡 Response OK:', response.ok);

    if (response.ok) {
      console.log('✅ OAuth Endpoint: Accessible');
      return { success: true, status: response.status };
    } else {
      const errorText = await response.text();
      console.error('❌ OAuth Endpoint Error:', errorText);
      return { success: false, status: response.status, error: errorText };
    }
  } catch (error) {
    console.error('💥 OAuth Endpoint Test Failed:', error);
    return { success: false, error: error };
  }
};

/**
 * Test the complete authentication flow
 */
export const testCompleteAuthFlow = async () => {
  console.log('🚀 Running Complete Authentication Test...');
  console.log('=' * 50);

  // Test 1: Supabase Auth
  const authResult = await testSupabaseAuth();
  console.log('\n');

  // Test 2: OAuth Endpoint
  const oauthResult = await testGoogleOAuthEndpoint();
  console.log('\n');

  // Summary
  console.log('📊 Test Summary:');
  console.log('================');
  
  if (authResult) {
    console.log(`👤 User Authentication: ${authResult.hasUser ? '✅' : '❌'}`);
    console.log(`🔐 Session Active: ${authResult.hasSession ? '✅' : '❌'}`);
    console.log(`🔑 Token Valid: ${authResult.isTokenValid ? '✅' : '❌'}`);
  } else {
    console.log('👤 User Authentication: ❌ Failed to test');
  }
  
  console.log(`🌐 OAuth Endpoint: ${oauthResult.success ? '✅' : '❌'}`);

  // Recommendations
  console.log('\n🎯 Recommendations:');
  console.log('===================');
  
  if (!authResult?.hasUser) {
    console.log('• User needs to log in through /login');
  }
  
  if (!authResult?.hasSession) {
    console.log('• Session is missing - try refreshing or logging in again');
  }
  
  if (!authResult?.isTokenValid) {
    console.log('• Token is expired - try refreshing the session');
  }
  
  if (!oauthResult.success) {
    if (oauthResult.status === 401) {
      console.log('• OAuth endpoint returns 401 - authentication issue confirmed');
    } else if (oauthResult.status === 404) {
      console.log('• OAuth endpoint not found - backend function may not be deployed');
    } else {
      console.log(`• OAuth endpoint error (${oauthResult.status}) - check backend logs`);
    }
  }

  if (authResult?.hasUser && authResult?.hasSession && authResult?.isTokenValid && oauthResult.success) {
    console.log('🎉 All tests passed! Google Calendar integration should work.');
  } else {
    console.log('⚠️  Some tests failed. Google Calendar integration may not work properly.');
  }

  return {
    auth: authResult,
    oauth: oauthResult,
    allPassed: authResult?.hasUser && authResult?.hasSession && authResult?.isTokenValid && oauthResult.success
  };
};

/**
 * Force refresh the Supabase session
 */
export const refreshSupabaseSession = async () => {
  console.log('🔄 Refreshing Supabase Session...');
  
  try {
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('❌ Session Refresh Failed:', error);
      return false;
    }
    
    console.log('✅ Session Refreshed Successfully');
    console.log('👤 User:', data.user?.email);
    console.log('🔑 New Token Expires:', new Date(data.session?.expires_at! * 1000).toLocaleString());
    
    return true;
  } catch (error) {
    console.error('💥 Session Refresh Error:', error);
    return false;
  }
};

/**
 * Clear all authentication state
 */
export const clearAuthState = async () => {
  console.log('🧹 Clearing Authentication State...');
  
  try {
    // Sign out from Supabase
    await supabase.auth.signOut();
    console.log('✅ Supabase: Signed out');
    
    // Clear localStorage
    localStorage.clear();
    console.log('✅ LocalStorage: Cleared');
    
    // Clear sessionStorage
    sessionStorage.clear();
    console.log('✅ SessionStorage: Cleared');
    
    console.log('🎯 Please refresh the page and log in again');
    
    return true;
  } catch (error) {
    console.error('💥 Clear Auth State Error:', error);
    return false;
  }
};

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).authTest = {
    testSupabaseAuth,
    testGoogleOAuthEndpoint,
    testCompleteAuthFlow,
    refreshSupabaseSession,
    clearAuthState,
  };
  
  console.log('🔧 Auth test utilities loaded. Use window.authTest in console:');
  console.log('• window.authTest.testCompleteAuthFlow() - Run all tests');
  console.log('• window.authTest.testSupabaseAuth() - Test Supabase auth');
  console.log('• window.authTest.testGoogleOAuthEndpoint() - Test OAuth endpoint');
  console.log('• window.authTest.refreshSupabaseSession() - Refresh session');
  console.log('• window.authTest.clearAuthState() - Clear all auth state');
}
