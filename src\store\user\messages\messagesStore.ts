import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { MessagesType } from "@/types/MessagesType";
import { UserType } from "@/types/UserType";
import { getSender } from "@/utils/messages/getSender";
import { getUsersMessagesToCurrentUser } from "@/utils/messages/getUsersMessagesToCurrentUser";
import { getConversation } from "@/utils/messages/getConversation";
import { MessageType } from "@/types/MessageType";
import { ReservationType } from "@/types/ReservationType";
import { sampleReservations } from "@/tempData/reservations";

type MessagesStore = {
  messages: MessagesType[];
  usersMessagesToCurrentUser: (currentUserID: string) => MessagesType[];
  messageSender: (senderID: string) => UserType | undefined;
  setMessages: (messages: MessagesType[]) => void;
  userConversation: MessageType[] | undefined;
  setUserConversation: (userID: string) => void;
  sendUserConversation: (conversationID: string, message: MessageType) => void;
  userReservation: {
    user: UserType | undefined;
    reservation: ReservationType | undefined;
  };
  getUserReservation: (userID: string) => void;
};

export const useMessagesStore = create<MessagesStore>()(
  immer((set) => ({
    messages: [],
    usersMessagesToCurrentUser: (currentUserID: string) =>
      getUsersMessagesToCurrentUser(currentUserID),
    messageSender: (senderID: string) => {
      try {
        return getSender(senderID);
      } catch {
        return undefined;
      }
    },
    setMessages: (messages: MessagesType[]) => set({ messages }),
    userConversation: [],
    setUserConversation: (conversationID: string) => {
      const conversation = getConversation(conversationID);
      set({ userConversation: conversation ?? [] });
    },

    sendUserConversation: (conversationID: string, message: MessageType) => {
      set((state) => {
        state.userConversation?.push(message);
      });
    },

    userReservation: {
      user: undefined,
      reservation: undefined,
    },
    getUserReservation: (userID: string) => {
      set({
        userReservation: {
          user: getSender(userID),
          reservation: sampleReservations.find(
            (item) => item.data.reserved.userId === userID
          ),
        },
      });
    },
  }))
);
