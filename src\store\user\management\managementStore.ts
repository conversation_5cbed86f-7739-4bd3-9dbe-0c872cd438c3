import { create } from "zustand";
import { sampleGuestHouses } from "@/tempData/guestHouses";
import { GuestHouseType } from "@/types/GuestHouseType";
import { sampleReservations } from "@/tempData/reservations";
import { ReservationType } from "@/types/ReservationType";

type ManagementStore = {
  selectedGuestHouse: string;
  setSelectedGuestHouse: (guestHouseId: string) => void;
  guesthouseReservationPreview: GuestHouseType[];
  setGuesthouseReservationPreview: () => void;
  reservations: ReservationType[];
  setReservations: (guesthouseID: string) => void;
};

export const useManagementStore = create<ManagementStore>()((set) => ({
  selectedGuestHouse: "",
  setSelectedGuestHouse: (guestHouseId) =>
    set({ selectedGuestHouse: guestHouseId }),
  guesthouseReservationPreview: [],
  setGuesthouseReservationPreview: () =>
    set({ guesthouseReservationPreview: sampleGuestHouses }),
  reservations: [],
  setReservations: (guesthouseID: string) =>
    set({
      reservations: sampleReservations.filter(
        (reservation) => reservation.data.reserved.guestHouseId === guesthouseID
      ),
    }),
}));
