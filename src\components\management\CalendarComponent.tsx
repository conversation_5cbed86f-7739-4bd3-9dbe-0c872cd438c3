import React from "react";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "moment/locale/ja";
import { CalendarNav } from "./CalendarNav";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { Event } from "./customEvents/Event";
import { BlackoutEvent } from "./customEvents/BlackoutEvent";
import { Button } from "@chakra-ui/react";
// import { events } from "@/tempData/events";
import { ReservationType } from "@/types/ReservationType";
import { useManagementStore } from "@/store/user/management/managementStore";
import { useRouter } from "next/router";
import { getBookingsForMonth } from "@/utils/backend/accommodations/getAccommodationsForMonth";

type ComponentProp = {
  setReservationId: (reservationId: string) => void;
};

export const CalendarComponent = ({ setReservationId }: ComponentProp) => {
  const [bookings, setBookings] = React.useState<any>(null);

  const router = useRouter();
  const guesthouseId = router.query["res-id"];
  const reservations = useManagementStore((state) => state.reservations);
  const setReservations = useManagementStore((state) => state.setReservations);

  React.useEffect(() => {
    setReservations(guesthouseId as string);
  }, [setReservations, guesthouseId]);

  React.useEffect(() => {
    const BookingsForMonth = async () => {
      const { bookings, error } = await getBookingsForMonth({
        accommodationId: guesthouseId as string,
        monthStartDate: new Date(date.getFullYear(), date.getMonth(), 1),
        monthEndDate: new Date(date.getFullYear(), date.getMonth() + 1, 0),
      });

      if (error) {
        console.error("Error fetching bookings:", error.message);
        return;
      }

      setBookings(bookings);
    };
    BookingsForMonth();
  }, []);
  console.log(bookings);

  moment.locale("ja");
  const localizer = momentLocalizer(moment);
  const CalendarComponent = Calendar as React.ComponentType<any>;
  const [date, setDate] = React.useState(new Date());

  const components = {
    event: ({ event }: any) => {
      const data = event?.data;
      if (data?.reserved) {
        return <Event reservationData={event} />;
      }
      if (data?.blackout) {
        return <BlackoutEvent appointment={data.blackout} />;
      }
      return null;
    },
  };

  const dayPropGetter = (date: Date) => {
    const dayStart = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );

    const hasBlackout = reservations.some((reservation) => {
      if (!reservation.data?.blackout) return false;

      const eventStart = new Date(reservation.start);
      const eventEnd = new Date(reservation.end);

      const normalizedStart = new Date(
        eventStart.getFullYear(),
        eventStart.getMonth(),
        eventStart.getDate()
      );
      const normalizedEnd = new Date(
        eventEnd.getFullYear(),
        eventEnd.getMonth(),
        eventEnd.getDate()
      );

      return dayStart >= normalizedStart && dayStart <= normalizedEnd;
    });

    const hasAppointment = reservations.some((reservation) => {
      if (!reservation.data?.reserved) return false;

      const eventStart = new Date(reservation.start);
      const eventEnd = new Date(reservation.end);

      const normalizedStart = new Date(
        eventStart.getFullYear(),
        eventStart.getMonth(),
        eventStart.getDate()
      );
      const normalizedEnd = new Date(
        eventEnd.getFullYear(),
        eventEnd.getMonth(),
        eventEnd.getDate()
      );

      return dayStart >= normalizedStart && dayStart <= normalizedEnd;
    });

    if (hasBlackout) {
      return {
        className: "blackout-date",
        style: {
          backgroundColor: "#eeeeee",
          color: "#D74132",
        },
      };
    }

    if (hasAppointment) {
      return {
        style: {
          backgroundColor: "#eeeeee",
          color: "#3174ad",
        },
      };
    }

    return {};
  };

  const displayEvents = reservations.filter(
    (reservation) => !reservation.data?.blackout
  );

  const DateHeader: React.FC<any> = ({ date, label, onDrillDown }) => {
    const hasBlackout = reservations.some((e) => {
      if (!e.data?.blackout) return false;
      const start = new Date(e.start);
      start.setHours(0, 0, 0, 0);
      const end = new Date(e.end);
      end.setHours(23, 59, 59, 999);

      const d = new Date(date);
      d.setHours(12, 0, 0, 0);
      return d >= start && d <= end;
    });

    const hasEvent = reservations.some((e) => {
      if (!e.data?.reserved) return false;
      const start = new Date(e.start);
      start.setHours(0, 0, 0, 0);
      const end = new Date(e.end);
      end.setHours(23, 59, 59, 999);

      const d = new Date(date);
      d.setHours(12, 0, 0, 0);
      return d >= start && d <= end;
    });

    return (
      <Button
        bg={"transparent"}
        type="button"
        className="rbc-button-link"
        onClick={onDrillDown}
        fontSize={".8rem"}
        style={
          hasBlackout
            ? { textDecoration: "line-through", textDecorationThickness: "2px" }
            : hasEvent
            ? { textDecoration: "line-through", textDecorationThickness: "2px" }
            : undefined
        }
      >
        {label}
      </Button>
    );
  };
  return (
    <>
      <CalendarNav date={date} setDate={setDate} />
      <div>
        <CalendarComponent
          components={{
            ...components,
            month: {
              dateHeader: DateHeader,
            },
          }}
          localizer={localizer}
          events={displayEvents}
          startAccessor="start"
          endAccessor="end"
          style={{ height: 500 }}
          culture="ja"
          toolbar={false}
          views={["month"]}
          date={date}
          onNavigate={setDate}
          dayPropGetter={dayPropGetter}
        />
      </div>
    </>
  );
};
