import { supabase } from "@/utils/backend/supabase";
import { start } from "repl";

type Props = {
  accommodationId: string;
  monthStartDate: Date;
  monthEndDate: Date;
};

export async function getBookingsForMonth({
  accommodationId,
  monthStartDate,
  monthEndDate,
}: Props) {
  // 1. Get current user
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    return { bookings: null, error: { message: "User not authenticated." } };
  }

  // 2. Format dates to ISO string without timezone offset name for Supabase compatibility
  const formatDateForSupabase = (date: Date) => {
    return date.toISOString().replace("Z", "");
  };

  const formattedMonthStart = formatDateForSupabase(monthStartDate);
  const formattedMonthEnd = formatDateForSupabase(monthEndDate);

  // 3. Verify permission: User must be an admin or the host of the accommodation
  const { data: accomm, error: accommError } = await supabase
    .from("accommodations")
    .select("host_id")
    .eq("id", accommodationId)
    .single();

  if (accommError) {
    return { bookings: null, error: { message: "Accommodation not found." } };
  }

  const { data: userProfile } = await supabase
    .from("users")
    .select("user_role")
    .eq("id", user.id)
    .single();

  if (userProfile?.user_role !== "admin" && accomm.host_id !== user.id) {
    return {
      bookings: null,
      error: { message: "Permission denied to view these bookings." },
    };
  }

  // 4. Fetch bookings that overlap with the selected month
  // A booking overlaps if (starts_before_end AND ends_after_start)
  const { data: bookings, error } = await supabase
    .from("bookings")
    .select(
      `
      id,
      check_in_date,
      check_out_date,
      status,
      external_sync_source,
      users ( full_name )
    `
    )
    .eq("accommodation_id", accommodationId)
    .lte("check_in_date", formattedMonthEnd) // Booking starts on or before the end of the month
    .gte("check_out_date", formattedMonthStart); // Booking ends on or after the start of the month

  if (error) {
    console.error("Error fetching bookings:", error.message);
    return { bookings: null, error };
  }
  // let filteredBookings;
  // if (bookings) {
  //   filteredBookings = bookings.map((booking: any) => ({
  //     id: booking.id,
  //     start: new Date(booking.check_in_date),
  //     end: new Date(booking.check_out_date),
  //     status: booking.status,
  //     external_sync_source: booking.external_sync_source,
  //     title: booking.users.full_name,
  //   }));
  //   return { bookings: filteredBookings, error: null };
  // }

  return { bookings, error: null };
}
