import { getRevenue } from "@/utils/sales/getRevenue";
import { getGrossProfit } from "@/utils/sales/getGrossProfit";
import { getTotalExpenses } from "@/utils/sales/getGuesthouseExpenses";
import { sampleReservations } from "@/tempData/reservations";
import { sampleExpenses } from "@/tempData/expenses";

export const getSalesOverview = () => {
  const numberOfReservations = sampleReservations.length;
  const revenue = getRevenue(sampleReservations);
  const expenses = getTotalExpenses(sampleExpenses);
  const profit = getGrossProfit({ revenue, expenses });
  return { numberOfReservations, revenue, profit };
};
