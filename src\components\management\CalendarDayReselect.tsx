import { Button, Flex, Grid, Text } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";

export const CalendarDayReselect = () => {
  const router = useRouter();
  const userID = router.query["res-id"];

  const [selectedDate, setSelectedDate] = React.useState<Date | null>(null);
  const [currentDate, setCurrentDate] = React.useState(new Date());
  const calendarHeaders = ["日", "月", "火", "水", "木", "金", "土"];

  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Start from the first Sunday of the calendar view
    const startDate = new Date(firstDay);
    startDate.setDate(firstDay.getDate() - firstDay.getDay());

    const days = [];
    const currentCalendarDate = new Date(startDate);

    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentCalendarDate));
      currentCalendarDate.setDate(currentCalendarDate.getDate() + 1);
    }

    return days;
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const goToPreviousMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
    );
  };

  const goToNextMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)
    );
  };

  return (
    <Flex flexDirection={"column"}>
      <Flex
        w={"100%"}
        justifyContent={"center"}
        alignItems={"center"}
        gap={"1rem"}
      >
        <Button onClick={goToPreviousMonth}>prev</Button>
        <Text fontSize={"lg"} fontWeight={"bold"}>
          {currentDate.getFullYear()}年 {currentDate.getMonth() + 1}月
        </Text>
        <Button onClick={goToNextMonth}>next</Button>
      </Flex>

      <Grid templateColumns={"repeat(7, 1fr)"} gap={"1rem"} mt={"1rem"}>
        {calendarHeaders.map((header) => (
          <Flex key={header} justifyContent={"center"} fontWeight={"bold"}>
            {header}
          </Flex>
        ))}
      </Grid>

      <Grid templateColumns={"repeat(7, 1fr)"} gap={"1rem"} mt={"0.5rem"}>
        {generateCalendarDays().map((day) => (
          <Flex
            key={day.toISOString()}
            justifyContent={"center"}
            alignItems={"center"}
            onClick={() => setSelectedDate(day)}
            bg={
              selectedDate?.toDateString() === day.toDateString()
                ? "blue.200"
                : isToday(day)
                ? "blue.100"
                : "white"
            }
            color={
              !isCurrentMonth(day)
                ? "gray.400"
                : isToday(day)
                ? "blue.600"
                : "black"
            }
            borderRadius={"md"}
            p={".5rem"}
            cursor={"pointer"}
            _hover={{ bg: "gray.100" }}
            border={isToday(day) ? "2px solid" : "1px solid"}
            borderColor={isToday(day) ? "blue.500" : "gray.200"}
            minH={"2.5rem"}
          >
            {day.getDate()}
          </Flex>
        ))}
      </Grid>
    </Flex>
  );
};
