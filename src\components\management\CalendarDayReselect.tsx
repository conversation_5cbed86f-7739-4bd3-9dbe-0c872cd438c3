import { Flex, Grid, Box, GridItem } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";
import { CalendarNav } from "./CalendarNav";
import { calendarHeaders } from "@/constants/calendarHeaders";
import { generateCalendarDays } from "@/utils/calendar/generateCalendarDays";
import { isInSelectedEvent } from "@/utils/calendar/isInSelectedEvent";
import { isStartOfSelectedEvent } from "@/utils/calendar/isStartOfSelectedEvent";
import { isEndOfSelectedEvent } from "@/utils/calendar/isEndOfSelectedEvent";
import { generateWeeksInAMonth } from "@/utils/calendar/generateWeeksInAMonth";
import { isCurrentMonth } from "@/utils/calendar/isCurrentMonth";
import { ReservationType } from "@/types/ReservationType";
import { isUnavailableDate } from "@/utils/calendar/isUnavailableDate";
import { isDateOverlapping } from "@/utils/calendar/isDateOverlapping";
import { handleDateClick } from "@/utils/calendar/handleChangeDatesClicks";

type ComponentProp = {
  reservations: ReservationType[];
  selectedReservation?: ReservationType;
  handleReservationChange: (date: Date, toChange: "start" | "end") => void;
};

export const CalendarDayReselect = ({
  reservations,
  selectedReservation,
  handleReservationChange,
}: ComponentProp) => {
  const router = useRouter();
  const userID = router.query["plan-id"];

  const cellHeight = 4.1;
  const cellBorderRadius = ".8rem";

  const unavailableDates = React.useMemo(
    () =>
      reservations.filter(
        (reservation) =>
          reservation.id !== selectedReservation?.id &&
          reservation.data.reserved.status !== "cancelled"
      ),
    [reservations, selectedReservation]
  );

  const [currentDate, setCurrentDate] = React.useState(new Date());
  const [toChange, setToChange] = React.useState<"start" | "end">("end");

  return (
    <Flex flexDirection={"column"}>
      <CalendarNav date={currentDate} setDate={setCurrentDate} />

      <Grid
        templateColumns={"repeat(7, 1fr)"}
        gap={"1rem"}
        mt={"1rem"}
        borderBottom={"1px solid"}
        borderColor={"gray.200"}
        paddingBottom={".5rem"}
      >
        {calendarHeaders.map((header) => (
          <Flex key={header} justifyContent={"center"} fontSize={".9rem"}>
            {header}
          </Flex>
        ))}
      </Grid>

      <Flex flexDirection={"column"} position={"relative"}>
        <Grid templateColumns="repeat(7, 1fr)" gap=".3rem" mt="0.5rem">
          {generateCalendarDays({ currentDate }).map((day) => {
            const inUnavailableEvent = isUnavailableDate({
              day,
              unavailableDates,
            });

            const isStart = isStartOfSelectedEvent({
              day,
              selectedReservation,
            });
            const isEnd = isEndOfSelectedEvent({ day, selectedReservation });
            const wouldOverlap = isDateOverlapping(
              day,
              unavailableDates,
              selectedReservation,
              toChange
            );

            const isClickable = !inUnavailableEvent && !wouldOverlap;

            return (
              <Flex
                key={day.toISOString()}
                justifyContent="center"
                bg={
                  isStart || isEnd
                    ? "primary"
                    : inUnavailableEvent
                    ? "#dadadaff"
                    : "white"
                }
                color={
                  isStart || isEnd
                    ? "white"
                    : !isCurrentMonth({ date: day, currentDate })
                    ? "gray.400"
                    : "black"
                }
                borderRadius={cellBorderRadius}
                pt=".5rem"
                cursor={isClickable ? "pointer" : "not-allowed"}
                minH={cellHeight + "rem"}
                minW={"100%"}
                position={"relative"}
              >
                <Box
                  zIndex={100}
                  fontSize={".9rem"}
                  position={"absolute"}
                  top={0}
                  left={0}
                  right={0}
                  bottom={0}
                  cursor={isClickable ? "pointer" : "not-allowed"}
                  onClick={() =>
                    isClickable &&
                    handleDateClick({
                      day,
                      unavailableDates,
                      selectedReservation,
                      toChange,
                      setToChange,
                      handleReservationChange,
                    })
                  }
                />
                <Box
                  zIndex={99}
                  fontSize={".9rem"}
                  textDecoration={inUnavailableEvent ? "line-through" : "none"}
                >
                  {day.getDate()}
                </Box>
              </Flex>
            );
          })}
        </Grid>

        <Grid
          templateRows={"repeat(6, 1fr)"}
          gap={".3rem"}
          mt={"0.5rem"}
          position={"absolute"}
          top={"0"}
          w={"100%"}
          pointerEvents="none"
        >
          {generateWeeksInAMonth({ currentDate, generateCalendarDays }).map(
            (week, index) => (
              <Grid key={index} templateColumns={"repeat(7, 1fr)"}>
                {week.map((day) => {
                  const inRange = isInSelectedEvent({
                    day,
                    selectedReservation,
                  });
                  const isStart = isStartOfSelectedEvent({
                    day,
                    selectedReservation,
                  });
                  const isEnd = isEndOfSelectedEvent({
                    day,
                    selectedReservation,
                  });

                  let bg = "transparent";
                  let opacity = 0;
                  let borderRadius = "0";

                  if (inRange) {
                    bg = "primaryPalette.100";
                    opacity = 1;
                    borderRadius = "0";
                  }
                  if (isStart || isEnd) {
                    bg = "primary";
                    opacity = 1;
                    borderRadius = cellBorderRadius;
                  }

                  return (
                    <Box
                      key={day.toISOString()}
                      bg={
                        isStart || isEnd ? "primaryPalette.100" : "transparent"
                      }
                      borderLeftRadius={isStart ? cellBorderRadius : "none"}
                      borderRightRadius={isEnd ? cellBorderRadius : "none"}
                    >
                      <GridItem
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        minH={cellHeight + "rem"}
                        bg={bg}
                        opacity={opacity}
                        borderRadius={borderRadius}
                        position={"relative"}
                        zIndex={10}
                      />
                    </Box>
                  );
                })}
              </Grid>
            )
          )}
        </Grid>
      </Flex>
    </Flex>
  );
};
