import { ReservationType } from "@/types/ReservationType";
import { Flex, Text } from "@chakra-ui/react";
import { formatReservationDate } from "@/utils/calendar/formatDate";
import React from "react";

type props = {
  reservation?: ReservationType;
};

export const EventPreview = ({ reservation }: props) => {
  return (
    <Flex
      flexDirection={"column"}
      justifyContent={"center"}
      bg={"primaryPalette.50"}
      textAlign={"center"}
      py={".8rem"}
      gap={".3rem"}
      borderRadius={"1rem"}
    >
      <Text color={"primary"} fontWeight={"semibold"} fontSize={"1rem"}>
        {reservation && formatReservationDate(reservation!)}
      </Text>
      <Text color={"primary"} fontWeight={"semibold"} fontSize={".9rem"}>
        [{reservation?.data.reserved.specialRequests}]
      </Text>
    </Flex>
  );
};
