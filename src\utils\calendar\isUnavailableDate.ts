import { ReservationType } from "@/types/ReservationType";

type Props = {
  day: Date;
  unavailableDates: ReservationType[];
};

export const isUnavailableDate = ({ day, unavailableDates }: Props) => {
  return unavailableDates.some((event) => {
    const eventStart = new Date(event.start);
    const eventEnd = new Date(event.end);
    const dayStart = new Date(day.getFullYear(), day.getMonth(), day.getDate());
    const normalizedStart = new Date(
      eventStart.getFullYear(),
      eventStart.getMonth(),
      eventStart.getDate()
    );
    const normalizedEnd = new Date(
      eventEnd.getFullYear(),
      eventEnd.getMonth(),
      eventEnd.getDate()
    );
    return dayStart >= normalizedStart && dayStart <= normalizedEnd;
  });
};
