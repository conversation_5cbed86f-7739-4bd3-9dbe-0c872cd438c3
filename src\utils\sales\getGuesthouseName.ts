import { ReservationType } from "@/types/ReservationType";
import { GuestHouseType } from "@/types/GuestHouseType";

type Props = {
  reservation?: ReservationType;
  guesthouses: GuestHouseType[];
};

export const getGuestHouseName = ({
  reservation,
  guesthouses,
}: Props): string => {
  const name = guesthouses.find(
    (g) => g.id === reservation?.data.reserved.guestHouseId
  )?.name;
  return name ?? "Unknown";
};
