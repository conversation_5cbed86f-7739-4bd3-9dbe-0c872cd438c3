import { CalendarDayReselect } from "@/components/management/CalendarDayReselect";
import { CompleteModal } from "@/components/management/CompleteModal";
import { EventPreview } from "@/components/management/EventPreview";
import { Button, Flex, Select, useDisclosure } from "@chakra-ui/react";
import React, { useState } from "react";
import { DropDownMenu } from "@/components/management/DropDownMenu";
import { sampleGuestHouses } from "@/tempData/guestHouses";
import { sampleReservations } from "@/tempData/reservations";
import { useRouter } from "next/router";
import { useManagementStore } from "@/store/user/management/managementStore";

const Index = () => {
  const router = useRouter();
  const userID = router.query["plan-id"];
  const id =
    typeof userID === "string"
      ? userID
      : Array.isArray(userID)
      ? userID[0]
      : undefined;
  const { isOpen, onOpen, onClose } = useDisclosure();

  const reservations = sampleReservations.filter(
    (reservation) =>
      reservation.data.reserved.guestHouseId === router.query["res-id"]
  );

  const [selectedReservation, setSelectedReservation] = useState(
    reservations.find((reservation) => (id ? reservation.id === id : false))
  );

  const handleReservationChange = (date: Date, toChange: "start" | "end") => {
    if (!selectedReservation) return;
    const nextStart = toChange === "start" ? date : selectedReservation.start;
    const nextEnd = toChange === "end" ? date : selectedReservation.end;
    const [start, end] =
      nextStart <= nextEnd
        ? [nextStart, nextEnd]
        : toChange === "start"
        ? [nextStart, nextStart]
        : [nextEnd, nextEnd];
    setSelectedReservation({ ...selectedReservation, start, end });
  };

  return (
    <Flex flexDirection={"column"} gap={"1rem"}>
      <DropDownMenu guesthouses={sampleGuestHouses} />

      <CalendarDayReselect
        selectedReservation={selectedReservation}
        reservations={reservations}
        handleReservationChange={handleReservationChange}
      />
      <EventPreview reservation={selectedReservation} />
      <Button
        variant={"primary"}
        fontWeight={"semibold"}
        onClick={() => {
          onOpen();
        }}
      >
        追加する
      </Button>
      <CompleteModal isOpen={isOpen} onClose={onClose} />
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
