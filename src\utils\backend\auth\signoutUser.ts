import { supabase } from "@/utils/backend/supabase";

/**
 * Signs out the current user.
 * @returns {Promise<{error: object|null}>} An object containing an error if one occurred.
 */
export async function signOutUser() {
  const { error } = await supabase.auth.signOut();

  if (error) {
    console.error("Logout Error:", error.message);
  }

  // After this call, the user is considered logged out.
  // The frontend should then redirect the user to the login page.
  return { error };
}
