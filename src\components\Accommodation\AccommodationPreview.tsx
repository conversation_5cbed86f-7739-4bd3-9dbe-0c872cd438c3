import ArrowRight from "@/assets/icons/ArrowRight";
import { Flex, Grid, Text } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";
import { Accommodation } from "@/types/AccommodationType";

type props = {
  accommodation: Accommodation;
};

export const AccommodationPreview = ({ accommodation }: props) => {
  const router = useRouter();
  return (
    <Grid
      templateColumns={"1fr 2rem"}
      borderBottom={"1px solid"}
      borderColor={"gray.200"}
      py={"1rem"}
      onClick={() => {
        router.push(`/user/management/${accommodation.id}`);
      }}
    >
      <Flex flexDirection={"column"} gap={".4rem"}>
        <Text
          noOfLines={2}
          overflow={"hidden"}
          w={"90%"}
          fontWeight={"semibold"}
          fontSize={"1.1rem"}
        >
          {accommodation.accommodation_name}
        </Text>
        <Text
          noOfLines={1}
          overflow={"hidden"}
          w={"70%"}
          fontSize={".9rem"}
          color={"gray.400"}
        >
          {accommodation.tagline}
        </Text>
        <Flex mt={".5rem"} gap={"3rem"}>
          <Text>{accommodation.address}</Text>
          <Flex gap={".3rem"} alignItems={"center"} w={"6rem"}>
            {accommodation.status === "published" ? (
              <Flex
                bg={"green.200"}
                borderRadius={"full"}
                h={".7rem"}
                w={".7rem"}
              ></Flex>
            ) : (
              <Flex
                bg={"gray.300"}
                borderRadius={"full"}
                h={".7rem"}
                w={".7rem"}
              ></Flex>
            )}
            <Text fontWeight={"semibold"}>
              {accommodation.status === "public" ? "公開" : "非公開"}
            </Text>
          </Flex>
        </Flex>
      </Flex>
      <Flex justifyContent={"flex-end"} alignItems={"center"}>
        <ArrowRight />
      </Flex>
    </Grid>
  );
};
