import React, { useRef, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>uButton,
  Text,
  VStack,
  Flex,
} from "@chakra-ui/react";
import { ChevronDownIcon } from "@chakra-ui/icons";
import { GuestHouseType } from "@/types/GuestHouseType";
import { useRouter } from "next/router";

type props = {
  page?: string;
  guesthouses: GuestHouseType[];
};

export const DropDownMenu = ({ page, guesthouses }: props) => {
  const router = useRouter();
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [buttonWidth, setButtonWidth] = React.useState("100%");

  useEffect(() => {
    if (buttonRef.current) {
      setButtonWidth(`${buttonRef.current.offsetWidth}px`);
    }
  }, []);

  const [selectedProperty, setSelectedProperty] = React.useState(
    guesthouses[0]
  );

  return (
    <Menu>
      {({ isOpen }) => (
        <>
          <MenuButton
            ref={buttonRef}
            as={Button}
            rightIcon={
              <ChevronDownIcon
                boxSize={6}
                transition="transform 0.2s ease-in-out"
                transform={isOpen ? "rotate(180deg)" : "rotate(0deg)"}
              />
            }
            width="100%"
            height="3.5rem"
            justifyContent="space-between"
            textAlign="left"
            variant="outline"
            borderColor="gray.100"
            _hover={{ borderColor: "gray.300" }}
            _expanded={{ borderColor: "blue.500", boxShadow: "outline" }}
          >
            <Flex width="100%">
              <Text isTruncated display="block" fontWeight={"semibold"}>
                {selectedProperty.name}
              </Text>
              <Text isTruncated display="block" fontWeight={"semibold"}>
                {selectedProperty.location}
              </Text>
            </Flex>
          </MenuButton>

          <MenuList
            width={buttonWidth}
            minWidth="unset"
            maxWidth={buttonWidth}
            p={0}
            mt={1}
            borderColor="gray.100"
            boxShadow="md"
          >
            <VStack spacing={0} align="stretch" width="100%">
              {guesthouses.map((guesthouse) => (
                <MenuItem
                  key={guesthouse.id}
                  width="100%"
                  p={"1rem"}
                  m={0}
                  borderRadius={0}
                  _last={{ borderBottom: "none" }}
                  _hover={{ bg: "gray.50" }}
                  onClick={() => {
                    setSelectedProperty(guesthouse);

                    // page === "plan"
                    //   ? router.push(
                    //       `/user/management/${guesthouse.id}/${guesthouse.id}`
                    //     )
                    //   :
                    router.push(`/user/management/${guesthouse.id}`);
                  }}
                >
                  <Flex width="100%">
                    <Text isTruncated display="block">
                      {guesthouse.name}
                    </Text>
                    <Text color="gray.600" isTruncated display="block">
                      {guesthouse.location}
                    </Text>
                  </Flex>
                </MenuItem>
              ))}
            </VStack>
          </MenuList>
        </>
      )}
    </Menu>
  );
};
