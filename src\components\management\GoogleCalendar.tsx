import {
  Button,
  Flex,
  Grid,
  Text,
  Image,
  useToast,
  Box,
  Spinner,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
} from "@chakra-ui/react";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { supabase } from "@/utils/backend/supabase";

type GoogleAccount = {
  id: string;
  account_email: string;
};

type Calendar = {
  id: string;
  summary: string;
};

export const GoogleCalendar = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [connectedAccounts, setConnectedAccounts] = useState<GoogleAccount[]>(
    []
  );
  const [calendars, setCalendars] = useState<Calendar[]>([]);
  const [selectedAccountId, setSelectedAccountId] = useState<string>("");
  const [accommodation, setAccommodation] = useState<any>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const router = useRouter();
  const guesthouseId = router.query["res-id"];

  // Check connection status on component mount
  useEffect(() => {
    if (guesthouseId) {
      checkConnectionStatus();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [guesthouseId]);

  const checkConnectionStatus = async () => {
    setIsCheckingStatus(true);
    try {
      // First get accommodation details
      await getAccommodationDetails();

      // Then get connected Google accounts
      const accounts = await getConnectedGoogleAccounts();
      setConnectedAccounts(accounts);
    } catch (error) {
      console.error("Error checking connection status:", error);
    } finally {
      setIsCheckingStatus(false);
    }
  };

  const getAccommodationDetails = async () => {
    try {
      const { data, error } = await supabase
        .from("accommodations")
        .select("*")
        .eq("id", guesthouseId)
        .single();

      if (error) {
        console.error("Error fetching accommodation:", error);
        return;
      }

      setAccommodation(data);
    } catch (error) {
      console.error("Error in getAccommodationDetails:", error);
    }
  };

  const getConnectedGoogleAccounts = async (): Promise<GoogleAccount[]> => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .from("host_google_accounts")
        .select("id, account_email")
        .eq("host_id", user.id);

      return error ? [] : data;
    } catch (error) {
      console.error("Error fetching connected accounts:", error);
      return [];
    }
  };

  const fetchCalendarList = async (hostGoogleAccountId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke(
        "get-google-calendars",
        {
          body: { hostGoogleAccountId },
        }
      );

      if (error) {
        if (
          error.message.includes("404") ||
          error.message.includes("NOT_FOUND")
        ) {
          throw new Error("CALENDARS_FUNCTION_NOT_DEPLOYED");
        }
        throw new Error(error.message);
      }

      setCalendars(data.calendars || []);
      setSelectedAccountId(hostGoogleAccountId);
      onOpen();
    } catch (error: any) {
      if (error.message === "CALENDARS_FUNCTION_NOT_DEPLOYED") {
        toast({
          title: "Function Not Available",
          description:
            "Calendar listing function is not yet available. Please contact the backend team.",
          status: "warning",
          duration: 8000,
          isClosable: true,
        });
      } else {
        toast({
          title: "Error fetching calendars",
          description:
            "Failed to retrieve your Google calendars. Please try again.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
      console.error("Error fetching calendars:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const linkCalendarToAccommodation = async (googleCalendarId: string) => {
    if (!accommodation?.id) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from("accommodations")
        .update({
          host_google_account_id: selectedAccountId,
          google_calendar_id: googleCalendarId,
          is_google_calendar_sync_enabled: true,
        })
        .eq("id", accommodation.id);

      if (error) {
        throw new Error(error.message);
      }

      toast({
        title: "Calendar linked successfully",
        description: "Your Google Calendar has been successfully linked.",
        status: "success",
        duration: 5000,
        isClosable: true,
      });

      // Refresh the data
      await getAccommodationDetails();
      const accounts = await getConnectedGoogleAccounts();
      setConnectedAccounts(accounts);

      onClose();
    } catch (error) {
      toast({
        title: "Error linking calendar",
        description: "Failed to link your Google Calendar. Please try again.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      console.error("Error linking calendar:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const startGoogleConnection = async () => {
    setIsLoading(true);
    try {
      const currentPagePath = router.asPath;
      console.log("Starting Google OAuth from path:", currentPagePath);

      // Use the direct URL approach as specified in the documentation
      // This bypasses the need for authentication headers since the rewrite will handle it
      const startUrl = `/functions/google-oauth-start?redirect_to=${encodeURIComponent(
        currentPagePath
      )}`;
      console.log("Redirecting to:", startUrl);

      // Redirect directly without any checks - let the backend handle authentication
      window.location.href = startUrl;

      // Don't set isLoading to false here because we're redirecting away
    } catch (error: any) {
      setIsLoading(false);

      if (
        error.message.includes("404") ||
        error.message.includes("NOT_FOUND")
      ) {
        toast({
          title: "Function Not Deployed",
          description:
            "The Google OAuth function is not yet available. Please contact the backend team.",
          status: "warning",
          duration: 8000,
          isClosable: true,
        });
      } else {
        toast({
          title: "Connection Error",
          description:
            "Failed to start Google OAuth process. Please try again.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }

      console.error("Google OAuth error:", error);
    }
  };

  const handleDisconnect = async () => {
    if (!accommodation?.id) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from("accommodations")
        .update({
          host_google_account_id: null,
          google_calendar_id: null,
          is_google_calendar_sync_enabled: false,
        })
        .eq("id", accommodation.id);

      if (error) {
        throw new Error(error.message);
      }

      toast({
        title: "Calendar disconnected",
        description: "Google Calendar has been successfully disconnected.",
        status: "success",
        duration: 5000,
        isClosable: true,
      });

      // Refresh the data
      await getAccommodationDetails();
      const accounts = await getConnectedGoogleAccounts();
      setConnectedAccounts(accounts);
    } catch (error) {
      toast({
        title: "Error disconnecting calendar",
        description: "Failed to disconnect Google Calendar. Please try again.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      console.error("Error disconnecting calendar:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // If already connected, show disconnect option
  if (accommodation?.is_google_calendar_sync_enabled) {
    return (
      <Grid
        templateColumns={"1fr 6rem"}
        p={"1.3rem 1.5rem"}
        bg={"chatBackground"}
        borderRadius={"lg"}
      >
        <Flex alignItems={"center"}>
          <Image
            src={"/images/Google_Calendar_icon_2020.svg.png"}
            alt="Google Calendar"
            w={"auto"}
            h={"3rem"}
          />
          <Flex flexDirection={"column"} gap={".3rem"}>
            <Text ml={"1rem"} fontSize={"1.3rem"} fontWeight={"bold"}>
              Google Calendar
            </Text>
            {accommodation.host_google_account_id && (
              <Text ml={"1rem"} fontSize={"sm"}>
                {connectedAccounts.find(
                  (acc) => acc.id === accommodation.host_google_account_id
                )?.account_email || "Connected"}
              </Text>
            )}
          </Flex>
        </Flex>
        <Flex justifyContent={"flex-end"}>
          <Button
            py={"1.4rem"}
            bg={"red.500"}
            color={"white"}
            w={"100%"}
            onClick={handleDisconnect}
            isLoading={isLoading}
          >
            連動解除
          </Button>
        </Flex>
      </Grid>
    );
  }

  return (
    <>
      <Grid
        templateColumns={"1fr 6rem"}
        p={"1.3rem 1.5rem"}
        bg={"chatBackground"}
        borderRadius={"lg"}
        position="relative"
      >
        {isCheckingStatus && (
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            display="flex"
            alignItems="center"
            justifyContent="center"
            bg="rgba(255, 255, 255, 0.7)"
            borderRadius="lg"
            zIndex="1"
          >
            <Spinner size="xl" />
          </Box>
        )}

        <Flex alignItems={"center"}>
          <Image
            src={"/images/Google_Calendar_icon_2020.svg.png"}
            alt="Google Calendar"
            w={"auto"}
            h={"3rem"}
          />
          <Flex flexDirection={"column"} gap={".3rem"}>
            <Text ml={"1rem"} fontSize={"1.3rem"} fontWeight={"bold"}>
              Google Calendar
            </Text>
            {connectedAccounts.length > 0 && (
              <Text ml={"1rem"} fontSize={"sm"} color="gray.600">
                {connectedAccounts.length} account(s) connected
              </Text>
            )}
          </Flex>
        </Flex>
        <Flex justifyContent={"flex-end"}>
          <Button
            py={"1.4rem"}
            bg={"secondary"}
            color={"white"}
            w={"100%"}
            onClick={startGoogleConnection}
            isLoading={isLoading}
          >
            連動
          </Button>
        </Flex>

        {/* Connected accounts list */}
        {connectedAccounts.length > 0 && (
          <Box gridColumn="1 / -1" mt={4}>
            <Text fontWeight="bold" mb={2}>
              Connected Accounts:
            </Text>
            {connectedAccounts.map((account) => (
              <Box
                key={account.id}
                p={2}
                bg="gray.100"
                borderRadius="md"
                mb={2}
              >
                <Text>{account.account_email}</Text>
                <Button
                  size="sm"
                  mt={2}
                  onClick={() => fetchCalendarList(account.id)}
                  isLoading={isLoading}
                >
                  Select Calendar
                </Button>
              </Box>
            ))}
          </Box>
        )}
      </Grid>

      {/* Calendar Selection Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Select a Calendar</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {calendars.length === 0 ? (
              <Text>No calendars found.</Text>
            ) : (
              <>
                <Text mb={4}>Select which calendar you want to sync with:</Text>
                {calendars.map((calendar) => (
                  <Box
                    key={calendar.id}
                    p={3}
                    borderWidth={1}
                    borderRadius="md"
                    mb={2}
                    cursor="pointer"
                    _hover={{ bg: "gray.100" }}
                    onClick={() => linkCalendarToAccommodation(calendar.id)}
                  >
                    <Text fontWeight="medium">{calendar.summary}</Text>
                    <Text fontSize="sm" color="gray.600">
                      ID: {calendar.id}
                    </Text>
                  </Box>
                ))}
              </>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};
