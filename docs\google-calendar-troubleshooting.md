# Google Calendar Integration Troubleshooting Guide

## 🚨 Current Issue: 401 Unauthorized Error

### Problem Description
When clicking the "連動" (Connect) button in the GoogleCalendar component, users encounter a 401 Unauthorized error.

**Error Details:**
- HTTP Status: 401 Unauthorized
- URL: `http://localhost:3000/functions/google-oauth-start?redirect_to=/user/management/{accommodation_id}`
- Location: `startGoogleConnection()` function in GoogleCalendar.tsx

### Root Cause Analysis

The 401 error occurs because the application was trying to call `/functions/google-oauth-start` directly, which bypasses the authentication layer. The correct flow should use the Next.js API route at `/api/google-oauth-start` which properly handles Supabase authentication.

### ✅ Solution Implemented

1. **Updated GoogleCalendar Component**: Modified `startGoogleConnection()` to:
   - Check Supabase session before making the request
   - Use `/api/google-oauth-start` instead of `/functions/google-oauth-start`
   - Provide better error messages for authentication issues

2. **Updated API Utility Functions**: Modified `googleCalendarAPI.ts` to use the correct endpoint

3. **Added Debug Component**: Created `AuthDebugger` component to help diagnose authentication issues

### 🔧 How to Test the Fix

1. **Navigate to the accommodation page**: `/user/management/{accommodation_id}`

2. **Use the Auth Debugger**: 
   - Look for the "Authentication Debugger" component at the top of the page
   - Click "Show Details" to see full authentication state
   - Check that "Status" shows "Fully Authenticated"

3. **Test the OAuth Endpoint**:
   - Click "Test OAuth Endpoint" button in the debugger
   - Should show "Endpoint Test Success" if working correctly

4. **Try the Google Calendar Connection**:
   - Click the "連動" (Connect) button in the Google Calendar card
   - Should redirect to Google OAuth consent screen (not show 401 error)

### 🔍 Diagnostic Steps

If you're still experiencing issues, follow these steps:

#### Step 1: Check Authentication State
```typescript
// In browser console, check Supabase session:
const { data: { session } } = await supabase.auth.getSession();
console.log('Session:', session);
```

#### Step 2: Verify Environment Variables
Ensure these are set in your `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

#### Step 3: Check User Login Status
- Ensure you're logged in through `/login`
- Check that the login process completes successfully
- Verify user data is stored in Zustand store

#### Step 4: Test API Route Directly
```bash
# Test the API route directly (replace with your accommodation ID)
curl -X GET "http://localhost:3000/api/google-oauth-start?redirect_to=/test" \
  -H "Cookie: your_session_cookie"
```

### 🚨 When to Contact Backend Team

Contact the backend team immediately if you encounter these specific errors:

1. **404 Not Found on Edge Functions**:
   ```
   Error: Function 'google-oauth-start' not found
   Error: Function 'get-google-calendars' not found
   ```

2. **Edge Function Internal Errors**:
   ```
   Error: Token exchange failed
   Error: Invalid OAuth configuration
   Error: Decryption failed
   ```

3. **Permission/Scope Errors**:
   ```
   Error: Insufficient OAuth scopes
   Error: Calendar API access denied
   ```

### 📋 Error Code Reference

| Error Code | Meaning | Solution |
|------------|---------|----------|
| 401 | Unauthorized - No valid session | Ensure user is logged in via Supabase |
| 403 | Forbidden - Invalid permissions | Check user role and accommodation ownership |
| 404 | Not Found - Edge function missing | Contact backend team |
| 500 | Internal Server Error | Check backend logs, contact backend team |

### 🔧 Common Fixes

#### Fix 1: Clear Browser Storage
```javascript
// Clear all auth-related storage
localStorage.clear();
sessionStorage.clear();
// Then log in again
```

#### Fix 2: Refresh Supabase Session
```javascript
// Force session refresh
await supabase.auth.refreshSession();
```

#### Fix 3: Check Cookie Settings
- Ensure cookies are enabled in browser
- Check for third-party cookie blocking
- Verify localhost cookie settings

### 🧪 Testing Checklist

Before reporting issues, verify:

- [ ] User is logged in through `/login`
- [ ] Supabase session exists and is valid
- [ ] Environment variables are set correctly
- [ ] Browser cookies are enabled
- [ ] No browser extensions blocking requests
- [ ] Network connectivity is stable
- [ ] Using correct accommodation ID in URL

### 📞 Escalation Process

1. **First**: Use the AuthDebugger component to gather diagnostic information
2. **Second**: Check browser console for detailed error messages
3. **Third**: Test the API endpoint directly using the debugger
4. **Finally**: If backend functions are missing/failing, contact backend team with:
   - Full error message from browser console
   - Steps to reproduce the issue
   - Authentication state from debugger
   - Network tab screenshots showing the failed request

### 🔄 Recovery Steps

If authentication gets into a bad state:

1. **Logout and Login Again**:
   ```javascript
   await supabase.auth.signOut();
   // Navigate to /login and sign in again
   ```

2. **Clear Application State**:
   ```javascript
   // Clear Zustand store
   useUserAuthStore.getState().clearUser();
   ```

3. **Hard Refresh**:
   - Press Ctrl+Shift+R (or Cmd+Shift+R on Mac)
   - This clears cached resources

### 📝 Debug Information to Collect

When reporting issues, include:

1. **Browser Console Errors**: Full error messages and stack traces
2. **Network Tab**: Screenshots of failed requests
3. **Auth Debugger Output**: Full authentication state
4. **Environment**: Browser version, OS, development vs production
5. **Steps to Reproduce**: Exact sequence of actions that cause the error

---

**Note**: The AuthDebugger component has been temporarily added to the accommodation page for troubleshooting. Remove it once the authentication issue is resolved.
