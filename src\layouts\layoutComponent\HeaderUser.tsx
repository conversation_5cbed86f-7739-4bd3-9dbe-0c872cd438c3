import {
  <PERSON><PERSON>,
  Image,
  <PERSON>u,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  Icon<PERSON>utton,
  <PERSON>,
} from "@chakra-ui/react";
import { ChevronDownIcon } from "@chakra-ui/icons";
import React from "react";
import { useRouter } from "next/router";
import { useUserAuthStore } from "@/store/user/users/userAuthStore";
import { signOutUser } from "@/utils/backend/auth/signoutUser";

export const HeaderUser = () => {
  const router = useRouter();
  const currentUser = useUserAuthStore((state) => state.currentUser);
  const clearUser = useUserAuthStore((state) => state.clearUser);

  const handleLogout = () => {
    signOutUser();
    clearUser();
    router.replace("/login");
  };

  return (
    <Flex
      w={"100dvw"}
      justifyContent={"space-between"}
      px={{ base: "1.5rem", md: "5rem" }}
      py={"2rem"}
      position={"fixed"}
      top={"0"}
      bg={"white"}
      zIndex={200}
    >
      <Image
        src={"/images/Column.png"}
        alt="Tokyo Guesthouse"
        w={"auto"}
        h={"2rem"}
      />
      <Flex alignItems={"center"} gap={"1rem"}>
        <Menu>
          <Flex alignItems={"center"} gap={"1rem"}>
            <Flex
              overflow={"hidden"}
              alignItems={"center"}
              justifyContent={"center"}
              borderRadius={"full"}
              w={"1.8rem"}
              h={"1.8rem"}
            >
              {currentUser && (
                <Image
                  src={
                    currentUser.avatar_url
                      ? currentUser.avatar_url
                      : "/images/webclip.png"
                  }
                  objectFit={"cover"}
                  objectPosition={"center"}
                  alt="Tokyo Guesthouse"
                  w={"auto"}
                  h={"1.5rem"}
                />
              )}
            </Flex>
            <Text fontSize={"sm"} fontWeight={"bold"}>
              {/* 田中 太郎 */}
              {currentUser && currentUser.full_name}
            </Text>
          </Flex>
          <MenuButton
            as={IconButton}
            icon={<ChevronDownIcon />}
            variant="ghost"
            transition="all 0.2s"
            _expanded={{ transform: "rotate(180deg)" }}
          />
          <MenuList>
            <MenuItem gap={"1rem"} onClick={handleLogout}>
              <Image
                src={"/images/Log-out.svg"}
                alt="Tokyo Guesthouse"
                w={"auto"}
                h={"1rem"}
              />
              ログアウト
            </MenuItem>
          </MenuList>
        </Menu>
      </Flex>
    </Flex>
  );
};
