import { CalendarComponent } from "@/components/management/CalendarComponent";
import { GoogleCalendar } from "@/components/management/GoogleCalendar";
import { AuthDebugger } from "@/components/debug/AuthDebugger";
import { LoginStatusChecker } from "@/components/debug/LoginStatusChecker";
import { Button, Flex } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";
import { sampleGuestHouses } from "@/tempData/guestHouses";
import { DropDownMenu } from "@/components/management/DropDownMenu";

// Import auth test utilities for debugging
import "@/utils/debug/authTest";
import "@/utils/auth/sessionFixer";
import "@/utils/auth/googleOAuthHelper";

const Index = () => {
  const router = useRouter();
  const guesthouseId = router.query["res-id"];
  const [reservationId, setReservationId] = React.useState<string | null>(null);

  return (
    <Flex flexDirection={"column"} gap={"1rem"}>
      <DropDownMenu guesthouses={sampleGuestHouses} />

      {/* Temporary debug components - remove after fixing auth issue */}
      <LoginStatusChecker />
      <AuthDebugger />

      <GoogleCalendar />

      <CalendarComponent setReservationId={setReservationId} />
      <Button
        variant={"primary"}
        fontWeight={"semibold"}
        onClick={() => {
          router.push(`/user/management/${guesthouseId}/${reservationId}`);
        }}
      >
        宿泊日程追加
      </Button>
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
