import type { NextApiRequest, NextApiResponse } from "next";
import { createPagesServerClient } from "@supabase/auth-helpers-nextjs";

/**
 * Debug endpoint to check authentication status
 * GET /api/debug/auth-status
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const supabase = createPagesServerClient({ req, res });

    // Get session information
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    // Get user information
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    // Check environment variables
    const envCheck = {
      NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      supabaseUrlValue: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 20) + '...',
    };

    // Check request headers
    const requestInfo = {
      method: req.method,
      hasCookie: !!req.headers.cookie,
      cookieLength: req.headers.cookie?.length || 0,
      userAgent: req.headers['user-agent']?.substring(0, 50) + '...',
      host: req.headers.host,
      referer: req.headers.referer,
    };

    // Session analysis
    const sessionAnalysis = {
      hasSession: !!session,
      hasAccessToken: !!session?.access_token,
      hasRefreshToken: !!session?.refresh_token,
      tokenType: session?.token_type,
      expiresAt: session?.expires_at,
      expiresIn: session?.expires_in,
      isExpired: session?.expires_at ? session.expires_at < Date.now() / 1000 : null,
      userId: session?.user?.id,
      userEmail: session?.user?.email,
      sessionError: sessionError?.message,
    };

    // User analysis
    const userAnalysis = {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      userRole: user?.role,
      createdAt: user?.created_at,
      lastSignInAt: user?.last_sign_in_at,
      userError: userError?.message,
    };

    // Overall status
    const overallStatus = {
      isAuthenticated: !!(session && user && session.access_token),
      canCallEdgeFunctions: !!(session?.access_token && !sessionError && !userError),
      issues: [] as string[],
    };

    // Identify issues
    if (!session) {
      overallStatus.issues.push('No session found');
    }
    if (!user) {
      overallStatus.issues.push('No user found');
    }
    if (!session?.access_token) {
      overallStatus.issues.push('No access token');
    }
    if (sessionError) {
      overallStatus.issues.push(`Session error: ${sessionError.message}`);
    }
    if (userError) {
      overallStatus.issues.push(`User error: ${userError.message}`);
    }
    if (session?.expires_at && session.expires_at < Date.now() / 1000) {
      overallStatus.issues.push('Session expired');
    }
    if (!req.headers.cookie) {
      overallStatus.issues.push('No cookies in request');
    }

    const debugInfo = {
      timestamp: new Date().toISOString(),
      overallStatus,
      sessionAnalysis,
      userAnalysis,
      requestInfo,
      envCheck,
    };

    // Log to server console
    console.log('🔍 Auth Status Debug:', JSON.stringify(debugInfo, null, 2));

    return res.status(200).json(debugInfo);
  } catch (error) {
    console.error('💥 Auth Status Debug Error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
  }
}
