import { sampleConversations } from "@/tempData/chat";
import { sampleMessages } from "@/tempData/messages";
import { MessageType } from "@/types/MessageType";

export const getConversation = (
  conversationID: string
): MessageType[] | undefined => {
  console.log(conversationID);
  const m = sampleMessages.find((message) => message.id === conversationID);

  if (m) {
    const conversations = m.messages.map((item) =>
      sampleConversations.find((conversation) => conversation.id === item)
    );

    return conversations.filter(
      (conv): conv is MessageType => conv !== undefined
    );
  }

  return undefined;
};
