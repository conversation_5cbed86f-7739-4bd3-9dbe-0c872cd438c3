* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: var(--font-pretendard);
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-pretendard);
}

form {
  all: inherit;
}

.rbc-event-content {
  background-color: #d74132 !important;
}

.rbc-event,
.rbc-selected,
.rbc-event-continues-after {
  background-color: #d74132 !important;
}
.rbc-row-segment {
  border-radius: 50rem !important;
}

.rbc-event,
.rbc-event-allday,
.rbc-event-continues-prior {
  border-radius: 50rem !important;
}
.rbc-day-bg {
  border: none !important;
  border-radius: 0.5rem !important;
}
.rbc-row-bg {
  border: none !important;
  gap: 0.2rem !important;
  height: 80% !important;
}
.rbc-month-row {
  border: none !important;
}
.rbc-month-view {
  gap: 0.2rem !important;
  border: none !important;
}
.rbc-off-range-bg {
  background-color: white !important;
}
.rbc-month-header {
  border-bottom: 1px solid #e7e7e7 !important;
  padding-bottom: 1rem;
}
.rbc-header {
  border: none !important;
  color: #7b8088 !important;
}

.rbc-date-cell {
  display: flex;
  flex-direction: column;
}

/* .rbc-date-cell.rbc-off-range {
  text-decoration: line-through !important;
} */

/* .rbc-button-link {
  text-decoration: inherit !important;
} */
