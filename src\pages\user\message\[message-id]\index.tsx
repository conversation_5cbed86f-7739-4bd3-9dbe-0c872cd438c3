import PhoneBookIcon from "@/assets/icons/PhoneBookIcon";
import SendIcon from "@/assets/icons/SendIcon";
import IncomingChatHead from "@/components/message/IncomingChatHead";
import SentChatHead from "@/components/message/SentChatHead";
import { Button, Flex, Grid, Image, Text, Textarea } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";
import { useUserAuthStore } from "@/store/user/users/userAuthStore";
import { useMessagesStore } from "@/store/user/messages/messagesStore";

const Index = () => {
  const router = useRouter();
  const messageSender = useMessagesStore((state) => state.messageSender);
  const setUserConversation = useMessagesStore(
    (state) => state.setUserConversation
  );
  const sendUserConversation = useMessagesStore(
    (state) => state.sendUserConversation
  );

  const messages = useMessagesStore((state) => state.userConversation);
  const currentUser = useUserAuthStore((state) => state.currentUser);

  const messagesID = router.query["message-id"];
  const sender = React.useMemo(() => {
    const id = router.query.sender;
    if (typeof id !== "string") return undefined;
    try {
      return messageSender(id);
    } catch {
      return undefined;
    }
  }, [router.query.sender, messageSender]);

  const [message, setMessage] = React.useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
  };

  const handleSend = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!currentUser || !sender || typeof messagesID !== "string") return;
    const nextIndex = (messages?.length ?? 0) + 1;
    sendUserConversation(messagesID, {
      id: `msg-${nextIndex}-${Date.now()}`,
      content: message,
      senderId: currentUser.id,
      receiverId: sender.id,
      timestamp: new Date(),
      viewed: false,
      status: "sent",
      conversationId: messagesID,
    });
    setMessage("");
  };

  React.useEffect(() => {
    if (messagesID) {
      setUserConversation(messagesID as string);
    }
  }, [messagesID, setUserConversation]);

  return (
    <Flex flexDirection={"column"}>
      <Grid
        gap={"1rem"}
        alignItems={"center"}
        pb={"1rem"}
        borderBottom={"1px solid"}
        borderColor={"gray.200"}
        gridTemplateColumns={"5rem 1fr 5rem"}
      >
        <Flex
          overflow={"hidden"}
          alignItems={"center"}
          justifyContent={"center"}
          borderRadius={"1rem"}
          w={"5rem"}
          h={"5rem"}
        >
          <Image
            src={sender && sender.image}
            objectFit={"cover"}
            objectPosition={"center"}
            alt="Tokyo Guesthouse"
            w={"auto"}
            h={"5rem"}
          />
        </Flex>
        <Flex flexDirection={"column"} gap={".4rem"}>
          <Text fontWeight={"bold"} fontSize={"1.2rem"}>
            {sender && sender.name}
          </Text>
          <Text fontSize={".9rem"} color={"gray.500"}>
            {sender && sender.address}
          </Text>
        </Flex>
        <Flex justifyContent={"flex-end"}>
          <Button
            variant={"outline"}
            p={0}
            w={"3.5rem"}
            onClick={() => {
              router.push(`/user/message/message-detail-info/${sender?.id}`);
            }}
          >
            詳細
          </Button>
        </Flex>
      </Grid>

      {messages &&
        messages.map((item) =>
          currentUser && item.senderId === currentUser.id ? (
            <SentChatHead
              key={item.id}
              message={item}
              currentUser={currentUser}
            />
          ) : (
            sender && (
              <IncomingChatHead key={item.id} message={item} sender={sender} />
            )
          )
        )}

      <Flex flexDirection={"column"}>
        <form onSubmit={handleSend}>
          <Flex position={"relative"}>
            <Textarea
              placeholder="メッセージを入力してください…"
              size="lg"
              borderRadius={"lg"}
              p={"1rem"}
              px={"4rem"}
              mt={"auto"}
              mb={"1rem"}
              h={"3rem"}
              fontSize={".85rem"}
              name="message"
              value={message}
              onChange={handleInputChange}
            />
            <Button
              position={"absolute"}
              variant={"ghost"}
              left={0}
              bottom={"1.5rem"}
              cursor={"pointer"}
            >
              <PhoneBookIcon />
            </Button>
            <Button
              position={"absolute"}
              variant={"ghost"}
              right={0}
              bottom={"1.5rem"}
              cursor={"pointer"}
              type="submit"
              disabled={message === ""}
              opacity={message === "" ? 0.3 : 1}
              zIndex={100}
            >
              <SendIcon />
            </Button>
          </Flex>
        </form>
      </Flex>
    </Flex>
  );
};

export default Index;

Index.getInitialProps = async () => {
  return {
    layoutType: "user",
  };
};
