import React from "react";
import {
  <PERSON>lex,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,
} from "@chakra-ui/react";

export const ErrorLoadingAccommodations = ({ error }: { error: string }) => {
  return (
    <Flex mt={"1rem"} flexDirection={"column"} h={"70dvh"}>
      <Alert status="error" borderRadius="md">
        <AlertIcon />
        {/* <AlertTitle mr={2}>Error loading accommodations!</AlertTitle> */}
        <AlertDescription>{error}</AlertDescription>
      </Alert>
      <Button
        mt={"14rem"}
        // colorScheme="blue"
        color={"gray.500"}
        variant={"ghost"}
        onClick={() => window.location.reload()}
      >
        Try Again
      </Button>
    </Flex>
  );
};
