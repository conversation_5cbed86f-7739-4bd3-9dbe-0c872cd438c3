import ArrowRight from "@/assets/icons/ArrowRight";
import { Flex, Grid, Text } from "@chakra-ui/react";
import React from "react";
import { useRouter } from "next/router";
import { GuestHouseType } from "@/types/GuestHouseType";

type props = {
  guesthouse: GuestHouseType;
};

export const ResPreview = ({ guesthouse }: props) => {
  const router = useRouter();
  return (
    <Grid
      templateColumns={"1fr 2rem"}
      borderBottom={"1px solid"}
      borderColor={"gray.200"}
      py={"1rem"}
      onClick={() => {
        router.push(`/user/management/${guesthouse.id}`);
      }}
    >
      <Flex flexDirection={"column"} gap={".4rem"}>
        <Text
          noOfLines={2}
          overflow={"hidden"}
          w={"90%"}
          fontWeight={"semibold"}
          fontSize={"1.1rem"}
        >
          {guesthouse.name}
        </Text>
        <Text
          noOfLines={1}
          overflow={"hidden"}
          w={"70%"}
          fontSize={".9rem"}
          color={"gray.400"}
        >
          {guesthouse.description}
        </Text>
        <Flex mt={".5rem"} gap={"3rem"}>
          <Text>{guesthouse.location}</Text>
          <Flex gap={".3rem"} alignItems={"center"} w={"6rem"}>
            {guesthouse.available ? (
              <Flex
                bg={"green.200"}
                borderRadius={"full"}
                h={".7rem"}
                w={".7rem"}
              ></Flex>
            ) : (
              <Flex
                bg={"gray.300"}
                borderRadius={"full"}
                h={".7rem"}
                w={".7rem"}
              ></Flex>
            )}
            <Text fontWeight={"semibold"}>
              {guesthouse.type === "public" ? "公開" : "非公開"}
            </Text>
          </Flex>
        </Flex>
      </Flex>
      <Flex justifyContent={"flex-end"} alignItems={"center"}>
        <ArrowRight />
      </Flex>
    </Grid>
  );
};
