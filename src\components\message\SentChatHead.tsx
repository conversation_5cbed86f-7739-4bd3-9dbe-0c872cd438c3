import React from "react";
import { Flex, Image, Text } from "@chakra-ui/react";
import { MessageType } from "@/types/MessageType";
import { UserType } from "@/types/UserType";

type props = {
  message: MessageType;
  currentUser: UserType;
};

export default function SentChatHead({ message, currentUser }: props) {
  return (
    <Flex gap={"1rem"} py={"1rem"} px={".8rem"} transform={"rotateY(180deg)"}>
      <Flex>
        <Flex
          overflow={"hidden"}
          alignItems={"center"}
          borderRadius={"1rem"}
          w={"3rem"}
          h={"3rem"}
          transform={"rotateY(180deg)"}
        >
          <Image
            src={currentUser.image}
            justifyContent={"center"}
            objectFit={"cover"}
            alt="Tokyo Guesthouse"
            w={"auto"}
            h={"3rem"}
          />
        </Flex>
      </Flex>
      <Flex
        flexDirection={"column"}
        ml={"1rem"}
        gap={".4rem"}
        bg={"chatBackground"}
        p={"1rem"}
        borderRadius={"xl"}
        position={"relative"}
        zIndex={2}
        transform={"rotateY(180deg)"}
      >
        <Image
          position={"absolute"}
          src={"/images/Polygon-1.jpg"}
          alt="Tokyo Guesthouse"
          w={"auto"}
          h={"1rem"}
          top={"1rem"}
          right={"-1rem"}
          zIndex={1}
          transform={"rotateY(180deg)"}
        />
        <Text fontWeight={"bold"} fontSize={"1.2rem"}>
          {currentUser.name}
        </Text>
        <Text fontSize={".9rem"}>{message.content}</Text>
      </Flex>
    </Flex>
  );
}
