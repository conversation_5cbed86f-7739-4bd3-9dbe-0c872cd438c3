import React from "react";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "moment/locale/ja";
import { CalendarNav } from "./CalendarNav";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { useRouter } from "next/router";
import { getBookingsForMonth } from "@/utils/backend/accommodations/getAccommodationsForMonth";
import { BookingCalendarType } from "@/types/BookingCalendarType";
import { Event } from "./customEvents/Event";
import { Booking } from "./customEvents/Booking";
import { LoadingComponent } from "../ui/LoadingComponent";
import { getAccommodationDetails } from "@/utils/backend/accommodations/getAccommodationDetails";

export const BookingsCalendarComponent = () => {
  const router = useRouter();
  const guesthouseId = router.query["res-id"];

  const localizer = momentLocalizer(moment);
  moment.locale("ja");
  const [date, setDate] = React.useState(new Date());
  const CalendarComponent = Calendar as React.ComponentType<any>;

  const [bookings, setBookings] = React.useState<BookingCalendarType[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  const [accommodation, setAccommodation] = React.useState<any>(null);

  React.useEffect(() => {
    const BookingsForMonth = async () => {
      const { bookings, error } = await getBookingsForMonth({
        accommodationId: guesthouseId as string,
        monthStartDate: new Date(date.getFullYear(), date.getMonth(), 1),
        monthEndDate: new Date(date.getFullYear(), date.getMonth() + 1, 0),
      });

      if (error) {
        setError(error.message);
        console.error("Error fetching bookings:", error.message);
        return;
      }

      // if (bookings) {
      //   const formattedBookings: BookingCalendarType[] = bookings.map(
      //     (booking: any) => ({
      //       id: booking.id,
      //       start: booking.check_in_date,
      //       end: booking.check_out_date,
      //       title: booking.users.full_name,

      //       className: "bg-white",
      //       style: { backgroundColor: "#fff", color: "#000" },
      //     })
      //   );
      //   setBookings(formattedBookings);
      // }
      setBookings(bookings as any);

      setLoading(false);
      setError(null);
    };
    BookingsForMonth();
  }, [guesthouseId, date]);

  // console.log(bookings);

  React.useEffect(() => {
    const fetchAccommodationDetails = async () => {
      const { accommodation, error } = await getAccommodationDetails(
        guesthouseId as string
      );
      if (error) {
        setError(error.message);
        console.error("Error fetching accommodation details:", error.message);
        return;
      }

      setAccommodation(accommodation);
    };
    fetchAccommodationDetails();
  }, [guesthouseId]);

  // console.log(accommodation);

  const components = {
    event: ({ event }: any) => {
      if (event) {
        return <Booking booking={event} />;
      }

      return null;
    },
  };

  // console.log(bookings);

  if (loading) return <LoadingComponent />;

  return (
    <>
      <CalendarNav date={date} setDate={setDate} />
      <div>
        <CalendarComponent
          components={components}
          localizer={localizer}
          events={bookings}
          startAccessor="start"
          endAccessor="end"
          style={{ height: 500 }}
          culture="ja"
          toolbar={false}
          views={["month"]}
          date={date}
          onNavigate={setDate}
          //   dayPropGetter={dayPropGetter}
        />
      </div>
    </>
  );
};
