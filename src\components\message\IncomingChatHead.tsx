import React from "react";
import { Flex, Image, Text } from "@chakra-ui/react";
import { MessageType } from "@/types/MessageType";
import { UserType } from "@/types/UserType";

type props = {
  message: MessageType;
  sender: UserType;
};

export default function IncomingChatHead({ message, sender }: props) {
  return (
    <Flex gap={"1rem"} py={"1rem"} px={".8rem"}>
      <Flex>
        <Flex
          overflow={"hidden"}
          alignItems={"center"}
          justifyContent={"center"}
          borderRadius={"1rem"}
          w={"3rem"}
          h={"3rem"}
        >
          <Image
            src={sender.image}
            alt="Tokyo Guesthouse"
            objectFit={"cover"}
            objectPosition={"center"}
            w={"auto"}
            h={"5rem"}
          />
        </Flex>
      </Flex>
      <Flex
        flexDirection={"column"}
        ml={"1rem"}
        gap={".4rem"}
        bg={"chatBackground"}
        p={"1rem"}
        borderRadius={"xl"}
        position={"relative"}
        zIndex={2}
      >
        <Image
          position={"absolute"}
          src={"/images/Polygon-1.jpg"}
          alt="Tokyo Guesthouse"
          w={"auto"}
          h={"1rem"}
          top={"1rem"}
          left={"-1rem"}
          zIndex={1}
        />
        <Text fontWeight={"bold"} fontSize={"1.2rem"}>
          {sender.name}
        </Text>
        <Text fontSize={".9rem"}>{message.content}</Text>
      </Flex>
    </Flex>
  );
}
