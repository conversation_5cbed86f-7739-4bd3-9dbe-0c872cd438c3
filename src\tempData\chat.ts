import { MessageType } from "@/types/MessageType";

export const sampleConversations: MessageType[] = [
  {
    id: "msg-1",
    content: "Hey there! How are you doing?",
    senderId: "1",
    receiverId: "2",
    timestamp: new Date("2023-10-15T09:30:00Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-1",
  },
  {
    id: "msg-2",
    content: "I'm good, thanks! How about you?",
    senderId: "2",
    receiverId: "1",
    timestamp: new Date("2023-10-15T09:32:15Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-1",
  },
  {
    id: "msg-3",
    content: "Doing great! Just working on some projects.",
    senderId: "1",
    receiverId: "2",
    timestamp: new Date("2023-10-15T09:35:45Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-1",
  },
  {
    id: "msg-4",
    content: "Sounds interesting! Tell me more about it.",
    senderId: "2",
    receiverId: "1",
    timestamp: new Date("2023-10-15T09:40:20Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-1",
  },

  {
    id: "msg-5",
    content: "Are we still meeting tomorrow?",
    senderId: "1",
    receiverId: "3",
    timestamp: new Date("2023-10-16T14:20:00Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-2",
  },
  {
    id: "msg-6",
    content: "Yes, 3 PM at the usual place.",
    senderId: "3",
    receiverId: "1",
    timestamp: new Date("2023-10-16T14:22:30Z"),
    viewed: false,
    status: "delivered",
    conversationId: "conv-2",
  },
  {
    id: "msg-7",
    content: "Perfect! I'll bring the documents.",
    senderId: "1",
    receiverId: "3",
    timestamp: new Date("2023-10-16T14:25:10Z"),
    viewed: false,
    status: "delivered",
    conversationId: "conv-2",
  },

  {
    id: "msg-8",
    content: "Did you watch the game last night?",
    senderId: "1",
    receiverId: "4",
    timestamp: new Date("2023-10-17T11:05:00Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-3",
  },
  {
    id: "msg-9",
    content: "Yes! It was amazing, especially the final quarter!",
    senderId: "4",
    receiverId: "1",
    timestamp: new Date("2023-10-17T11:07:22Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-3",
  },
  {
    id: "msg-10",
    content: "I know, right? That last-minute goal was incredible!",
    senderId: "1",
    receiverId: "4",
    timestamp: new Date("2023-10-17T11:08:15Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-3",
  },
  {
    id: "msg-11",
    content: "We should go to the next game together!",
    senderId: "4",
    receiverId: "1",
    timestamp: new Date("2023-10-17T11:09:05Z"),
    viewed: true,
    status: "read",
    conversationId: "conv-3",
  },

  {
    id: "msg-12",
    content: "Hi, I need to discuss the project timeline with you.",
    senderId: "5",
    receiverId: "1",
    timestamp: new Date("2023-10-18T08:45:00Z"),
    viewed: false,
    status: "sent",
    conversationId: "conv-4",
  },
];
