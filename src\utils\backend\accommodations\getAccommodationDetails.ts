import { supabase } from "@/utils/backend/supabase";

/**
 * Fetches the details for a single accommodation, including its sync status.
 * @param {string} accommodationId - The UUID of the accommodation.
 * @returns {Promise<{accommodation: object|null, error: object|null}>}
 */
export async function getAccommodationDetails(accommodationId: string) {
  const { data: accommodation, error } = await supabase
    .from("accommodations")
    .select(
      `
      id,
      host_id,
      accommodation_name,
      is_google_calendar_sync_enabled,
      google_calendar_id,
      host_google_accounts (
        id,
        account_email
      )
    `
    )
    .eq("id", accommodationId)
    .single();

  if (error) {
    console.error("Error fetching accommodation details:", error);
    return { accommodation: null, error };
  }

  return { accommodation, error: null };
}
