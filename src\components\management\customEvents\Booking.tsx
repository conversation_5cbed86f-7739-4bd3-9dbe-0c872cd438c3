import { Box } from "@chakra-ui/react";
import React, { useEffect } from "react";
import { useRouter } from "next/router";

export const Booking = (booking: any) => {
  const router = useRouter();
  const resId = router.query["res-id"];

  return (
    <Box
      w={"100%"}
      h={"100%"}
      color={"white"}
      borderRadius={"1rem"}
      px={"1rem"}
      onClick={() => {
        router.push(`/user/management/${resId}/${booking.booking.id}`);
      }}
    >
      {booking.booking.title}
    </Box>
  );
};
