import { sampleConversations } from "@/tempData/chat";
import { MessageType } from "@/types/MessageType";

type props = {
  message: string;
  conversationId: string;
  sender: string;
  receiver: string;
};

export const sendMessage = ({
  message,
  conversationId,
  sender,
  receiver,
}: props) => {
  const newMessage: MessageType = {
    id: `msg-${sampleConversations.length + 1}`,
    content: message,
    senderId: sender,
    receiverId: receiver,
    timestamp: new Date(),
    viewed: false,
    status: "sent",
    conversationId: conversationId,
  };

  sampleConversations.push(newMessage);
};
