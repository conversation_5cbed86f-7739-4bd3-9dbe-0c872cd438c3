/**
 * Simple login status checker to verify authentication state
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Text,
  Button,
  VStack,
  HStack,
  Badge,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { supabase } from '@/utils/backend/supabase';
import { useUserAuthStore } from '@/store/user/users/userAuthStore';

export const LoginStatusChecker = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [clientAuth, setClientAuth] = useState<any>(null);
  const [serverAuth, setServerAuth] = useState<any>(null);
  const router = useRouter();
  const toast = useToast();
  
  // Get Zustand auth state
  const currentUser = useUserAuthStore((state) => state.currentUser);
  const clearUser = useUserAuthStore((state) => state.clearUser);

  useEffect(() => {
    checkClientAuth();
  }, []);

  const checkClientAuth = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const { data: { user } } = await supabase.auth.getUser();
      
      setClientAuth({
        hasSession: !!session,
        hasUser: !!user,
        hasAccessToken: !!session?.access_token,
        isTokenValid: session?.expires_at ? session.expires_at > Date.now() / 1000 : false,
        userEmail: user?.email,
        sessionExpiry: session?.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : null,
      });
    } catch (error) {
      console.error('Client auth check error:', error);
      setClientAuth({ error: error });
    }
  };

  const checkServerAuth = async () => {
    setIsChecking(true);
    try {
      const response = await fetch('/api/debug/auth-status', {
        credentials: 'include',
      });
      
      const data = await response.json();
      setServerAuth(data);
      
      if (!data.overallStatus?.isAuthenticated) {
        toast({
          title: 'Server Authentication Issue',
          description: data.overallStatus?.issues?.join(', ') || 'Unknown issue',
          status: 'warning',
          duration: 5000,
        });
      }
    } catch (error) {
      setServerAuth({ error: error });
      toast({
        title: 'Server Check Failed',
        description: 'Could not check server authentication status',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsChecking(false);
    }
  };

  const handleLogin = () => {
    router.push('/login');
  };

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      clearUser();
      localStorage.clear();
      sessionStorage.clear();
      
      toast({
        title: 'Logged Out',
        description: 'You have been logged out successfully',
        status: 'success',
        duration: 3000,
      });
      
      // Refresh the checks
      await checkClientAuth();
      setServerAuth(null);
    } catch (error) {
      toast({
        title: 'Logout Error',
        description: 'There was an error logging out',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const handleRefreshSession = async () => {
    try {
      const { error } = await supabase.auth.refreshSession();
      if (error) {
        throw error;
      }
      
      toast({
        title: 'Session Refreshed',
        description: 'Your session has been refreshed',
        status: 'success',
        duration: 3000,
      });
      
      await checkClientAuth();
    } catch (error) {
      toast({
        title: 'Refresh Failed',
        description: 'Could not refresh session - you may need to log in again',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const getOverallStatus = () => {
    if (clientAuth?.hasSession && clientAuth?.hasUser && clientAuth?.isTokenValid) {
      return { status: 'success', text: 'Authenticated' };
    } else if (clientAuth?.hasUser && !clientAuth?.isTokenValid) {
      return { status: 'warning', text: 'Session Expired' };
    } else {
      return { status: 'error', text: 'Not Authenticated' };
    }
  };

  const overallStatus = getOverallStatus();

  return (
    <Box p={4} borderWidth={1} borderRadius="md" bg="blue.50">
      <VStack align="stretch" spacing={4}>
        <HStack justify="space-between">
          <Text fontSize="lg" fontWeight="bold">
            🔐 Login Status
          </Text>
          <Badge 
            colorScheme={
              overallStatus.status === 'success' ? 'green' : 
              overallStatus.status === 'warning' ? 'yellow' : 'red'
            }
            fontSize="sm"
          >
            {overallStatus.text}
          </Badge>
        </HStack>

        {/* Quick Status */}
        <VStack align="stretch" spacing={2} fontSize="sm">
          <HStack>
            <Text fontWeight="medium">Client Session:</Text>
            <Badge colorScheme={clientAuth?.hasSession ? 'green' : 'red'}>
              {clientAuth?.hasSession ? 'Present' : 'Missing'}
            </Badge>
          </HStack>
          
          <HStack>
            <Text fontWeight="medium">Client User:</Text>
            <Badge colorScheme={clientAuth?.hasUser ? 'green' : 'red'}>
              {clientAuth?.hasUser ? clientAuth.userEmail : 'Missing'}
            </Badge>
          </HStack>
          
          <HStack>
            <Text fontWeight="medium">Token Valid:</Text>
            <Badge colorScheme={clientAuth?.isTokenValid ? 'green' : 'red'}>
              {clientAuth?.isTokenValid ? 'Yes' : 'No/Expired'}
            </Badge>
          </HStack>

          <HStack>
            <Text fontWeight="medium">Zustand Store:</Text>
            <Badge colorScheme={currentUser ? 'green' : 'red'}>
              {currentUser ? 'Has User' : 'Empty'}
            </Badge>
          </HStack>

          {serverAuth && (
            <HStack>
              <Text fontWeight="medium">Server Auth:</Text>
              <Badge colorScheme={serverAuth.overallStatus?.isAuthenticated ? 'green' : 'red'}>
                {serverAuth.overallStatus?.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
              </Badge>
            </HStack>
          )}
        </VStack>

        {/* Actions */}
        <HStack spacing={2} flexWrap="wrap">
          <Button size="sm" onClick={checkClientAuth}>
            Refresh Client
          </Button>
          <Button size="sm" onClick={checkServerAuth} isLoading={isChecking}>
            Check Server
          </Button>
          {clientAuth?.hasSession && (
            <Button size="sm" onClick={handleRefreshSession} colorScheme="blue">
              Refresh Session
            </Button>
          )}
          <Button size="sm" onClick={handleLogout} colorScheme="red">
            Logout
          </Button>
          <Button size="sm" onClick={handleLogin} colorScheme="green">
            Go to Login
          </Button>
        </HStack>

        {/* Recommendations */}
        {overallStatus.status !== 'success' && (
          <Alert status="warning" size="sm">
            <AlertIcon />
            <Box>
              <AlertTitle fontSize="sm">Action Required:</AlertTitle>
              <AlertDescription fontSize="xs">
                {!clientAuth?.hasUser && "You need to log in. "}
                {clientAuth?.hasUser && !clientAuth?.isTokenValid && "Your session has expired. Try refreshing or log in again. "}
                {serverAuth && !serverAuth.overallStatus?.isAuthenticated && "Server can't see your authentication. "}
                Click "Go to Login" to authenticate.
              </AlertDescription>
            </Box>
          </Alert>
        )}
      </VStack>
    </Box>
  );
};
