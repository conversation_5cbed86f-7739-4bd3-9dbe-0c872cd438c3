import { supabase } from "@/utils/backend/supabase";

export async function getAccommodationsList() {
  // 1. Get current user's session and ID
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      accommodations: null,
      error: { message: "User not authenticated." },
    };
  }

  // 2. Get user's role from the 'users' table
  const { data: userProfile, error: profileError } = await supabase
    .from("users")
    .select("user_role")
    .eq("id", user.id)
    .single();

  if (profileError || !userProfile) {
    return {
      accommodations: null,
      error: { message: "Could not find user profile." },
    };
  }

  // 3. Build the query based on the user's role
  let query = supabase
    .from("accommodations")
    .select("id, accommodation_name, address, status, tagline");

  // If the user is a host, filter by their user ID
  if (userProfile.user_role === "host") {
    query = query.eq("host_id", user.id);
  } else if (userProfile.user_role !== "admin") {
    // If user is neither admin nor host, return empty list and error
    return { accommodations: [], error: { message: "Permission denied." } };
  }

  // 4. Execute the query and sort the results
  const { data: accommodations, error } = await query.order("created_at", {
    ascending: false,
  });

  if (error) {
    console.error("Error fetching accommodations:", error.message);
    return { accommodations: null, error };
  }

  return { accommodations, error: null };
}
