import ArrowLeft from "@/assets/icons/ArrowLeft";
import ArrowRight from "@/assets/icons/ArrowRight";
import { Button, Flex, Text } from "@chakra-ui/react";
import React from "react";

type ComponentProp = {
  date: Date;
  setDate: React.Dispatch<React.SetStateAction<Date>>;
};

export const CalendarNav = ({ date, setDate }: ComponentProp) => {
  return (
    <Flex
      gap={"1rem"}
      alignItems={"center"}
      justifyContent={"center"}
      h={"4rem"}
    >
      <Button
        bg={"primary"}
        color={"white"}
        borderRadius={"full"}
        p={0}
        w={"2.4rem"}
        h={"2.4rem"}
        onClick={() => {
          setDate(new Date(date.getFullYear(), date.getMonth() - 1, 1));
        }}
      >
        <ArrowLeft width={25} height={25} color={"white"} />
      </Button>
      <Text fontSize={"1.5rem"} fontWeight={"bold"}>
        {date.getFullYear()}年 {date.getMonth() + 1}月
      </Text>
      <Button
        bg={"primary"}
        color={"white"}
        borderRadius={"full"}
        p={0}
        w={"2.4rem"}
        h={"2.4rem"}
        onClick={() => {
          setDate(new Date(date.getFullYear(), date.getMonth() + 1, 1));
        }}
      >
        <ArrowRight width={25} height={25} color={"white"} />
      </Button>
    </Flex>
  );
};
