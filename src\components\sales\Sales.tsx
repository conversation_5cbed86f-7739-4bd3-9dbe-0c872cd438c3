import React from "react";
import { Flex, Text } from "@chakra-ui/react";
import { formatAmount } from "@/utils/sales/formatAmount";
import { GrossProfitByGuesthouseType } from "@/types/sales/grossProfitByGuesthouseType";

type Props = {
  grossProfitByGuestHouse: GrossProfitByGuesthouseType;
};

export const Sales = ({ grossProfitByGuestHouse }: Props) => {
  return (
    <Flex
      justifyContent={"space-between"}
      py={"1.2rem"}
      pl={"1rem"}
      borderBottom={"1px solid"}
      borderColor={"gray.100"}
    >
      <Text
        fontSize={"1.1rem"}
        fontWeight={"bold"}
        noOfLines={1}
        w={"60%"}
        overflow={"hidden"}
      >
        {grossProfitByGuestHouse && grossProfitByGuestHouse.guesthouseName}
      </Text>

      <Text
        color={
          grossProfitByGuestHouse.grossProfit > 0 ? "secondary" : "red.500"
        }
        fontSize={"1rem"}
      >
        {formatAmount(grossProfitByGuestHouse.grossProfit)}
      </Text>
    </Flex>
  );
};
