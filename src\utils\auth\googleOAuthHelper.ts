/**
 * Google OAuth helper that handles authentication client-side
 * This bypasses the server-side session issues by handling auth on the client
 */

import { supabase } from '@/utils/backend/supabase';

export interface OAuthResult {
  success: boolean;
  error?: string;
  redirectUrl?: string;
}

/**
 * Start Google OAuth flow with client-side authentication
 * This approach sends the access token directly to the Edge Function
 */
export async function startGoogleOAuthClientSide(redirectTo: string): Promise<OAuthResult> {
  try {
    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      return {
        success: false,
        error: `Session error: ${sessionError.message}`
      };
    }

    if (!session?.access_token) {
      return {
        success: false,
        error: 'No valid session found. Please log in first.'
      };
    }

    // Call the Supabase Edge Function directly with the access token
    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/google-oauth-start`;
    const fullUrl = `${edgeFunctionUrl}?redirect_to=${encodeURIComponent(redirectTo)}`;

    console.log('🚀 Calling Edge Function directly:', fullUrl);

    // Make the request with the access token in the Authorization header
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 Edge Function Response:', {
      status: response.status,
      statusText: response.statusText,
      redirected: response.redirected,
      url: response.url,
    });

    if (response.redirected) {
      // If the response was redirected, that means the OAuth flow started successfully
      return {
        success: true,
        redirectUrl: response.url
      };
    }

    if (response.ok) {
      // Check if the response contains a redirect URL
      const responseText = await response.text();
      console.log('📄 Edge Function Response Body:', responseText);
      
      // Try to parse as JSON in case it contains a redirect URL
      try {
        const responseData = JSON.parse(responseText);
        if (responseData.redirectUrl) {
          return {
            success: true,
            redirectUrl: responseData.redirectUrl
          };
        }
      } catch {
        // Not JSON, that's okay
      }

      // If we get here, the function responded OK but didn't redirect
      // This might mean we need to handle the response differently
      return {
        success: false,
        error: 'Edge function responded but did not provide redirect URL'
      };
    }

    // Handle error responses
    const errorText = await response.text();
    let errorData;
    try {
      errorData = JSON.parse(errorText);
    } catch {
      errorData = { error: errorText };
    }

    return {
      success: false,
      error: `Edge function error (${response.status}): ${errorData.error || errorText}`
    };

  } catch (error) {
    console.error('💥 Google OAuth Client-Side Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Alternative approach: Use a form submission to handle the OAuth flow
 * This can sometimes work better with redirects
 */
export function startGoogleOAuthWithForm(redirectTo: string, accessToken: string): void {
  // Create a hidden form that submits to the Edge Function
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/google-oauth-start`;
  form.style.display = 'none';

  // Add redirect_to parameter
  const redirectInput = document.createElement('input');
  redirectInput.type = 'hidden';
  redirectInput.name = 'redirect_to';
  redirectInput.value = redirectTo;
  form.appendChild(redirectInput);

  // Add authorization header as a hidden input (some Edge Functions can read this)
  const authInput = document.createElement('input');
  authInput.type = 'hidden';
  authInput.name = 'authorization';
  authInput.value = `Bearer ${accessToken}`;
  form.appendChild(authInput);

  // Add to DOM and submit
  document.body.appendChild(form);
  form.submit();
  
  // Clean up
  document.body.removeChild(form);
}

/**
 * Test if the Edge Function is accessible with current authentication
 */
export async function testEdgeFunctionAccess(): Promise<{
  success: boolean;
  error?: string;
  details?: any;
}> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session?.access_token) {
      return {
        success: false,
        error: 'No access token available'
      };
    }

    // Try to call a simple Edge Function endpoint (if available)
    // or test the google-oauth-start with a test parameter
    const testUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/google-oauth-start?test=true`;
    
    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    return {
      success: response.ok,
      error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
      details: {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).googleOAuthHelper = {
    startGoogleOAuthClientSide,
    startGoogleOAuthWithForm,
    testEdgeFunctionAccess,
  };
  
  console.log('🔧 Google OAuth helper loaded. Use window.googleOAuthHelper in console:');
  console.log('• window.googleOAuthHelper.testEdgeFunctionAccess() - Test Edge Function access');
  console.log('• window.googleOAuthHelper.startGoogleOAuthClientSide("/test") - Start OAuth flow');
}
