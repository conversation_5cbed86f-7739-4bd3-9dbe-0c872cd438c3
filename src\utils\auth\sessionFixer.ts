/**
 * Session fixer utilities to resolve common authentication issues
 * These functions help diagnose and fix session-related problems
 */

import { supabase } from '@/utils/backend/supabase';

export interface SessionDiagnostic {
  hasSession: boolean;
  hasUser: boolean;
  hasAccessToken: boolean;
  isTokenValid: boolean;
  sessionAge: number | null;
  issues: string[];
  recommendations: string[];
}

/**
 * Diagnose the current session state
 */
export async function diagnoseSession(): Promise<SessionDiagnostic> {
  const diagnostic: SessionDiagnostic = {
    hasSession: false,
    hasUser: false,
    hasAccessToken: false,
    isTokenValid: false,
    sessionAge: null,
    issues: [],
    recommendations: [],
  };

  try {
    // Check session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    diagnostic.hasSession = !!session;

    if (sessionError) {
      diagnostic.issues.push(`Session error: ${sessionError.message}`);
    }

    // Check user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    diagnostic.hasUser = !!user;

    if (userError) {
      diagnostic.issues.push(`User error: ${userError.message}`);
    }

    // Check access token
    if (session?.access_token) {
      diagnostic.hasAccessToken = true;
      
      // Check if token is valid (not expired)
      if (session.expires_at) {
        const now = Date.now() / 1000;
        diagnostic.isTokenValid = session.expires_at > now;
        diagnostic.sessionAge = now - (session.expires_at - (session.expires_in || 3600));
        
        if (!diagnostic.isTokenValid) {
          diagnostic.issues.push('Access token has expired');
        }
      }
    } else {
      diagnostic.issues.push('No access token found');
    }

    // Generate recommendations
    if (!diagnostic.hasSession) {
      diagnostic.recommendations.push('User needs to log in');
    }
    
    if (!diagnostic.hasUser) {
      diagnostic.recommendations.push('User session is invalid, try logging in again');
    }
    
    if (!diagnostic.hasAccessToken) {
      diagnostic.recommendations.push('Session exists but no access token, try refreshing session');
    }
    
    if (!diagnostic.isTokenValid) {
      diagnostic.recommendations.push('Token expired, refresh session or log in again');
    }

    if (diagnostic.sessionAge && diagnostic.sessionAge > 3600) {
      diagnostic.recommendations.push('Session is old, consider refreshing');
    }

  } catch (error) {
    diagnostic.issues.push(`Diagnostic error: ${error}`);
    diagnostic.recommendations.push('Clear browser storage and log in again');
  }

  return diagnostic;
}

/**
 * Attempt to refresh the current session
 */
export async function refreshSession(): Promise<{ success: boolean; error?: string }> {
  try {
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    if (data.session) {
      return { success: true };
    }
    
    return { success: false, error: 'No session returned after refresh' };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Clear all authentication state and sign out
 */
export async function clearAuthState(): Promise<{ success: boolean; error?: string }> {
  try {
    // Sign out from Supabase
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.warn('Supabase signout error:', error);
    }
    
    // Clear browser storage
    if (typeof window !== 'undefined') {
      localStorage.clear();
      sessionStorage.clear();
    }
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Test if the current session can make authenticated API calls
 */
export async function testAuthenticatedCall(): Promise<{ success: boolean; error?: string; details?: any }> {
  try {
    const response = await fetch('/api/debug/auth-status', {
      method: 'GET',
      credentials: 'include',
    });

    const data = await response.json();

    if (response.ok && data.overallStatus?.isAuthenticated) {
      return { success: true, details: data };
    } else {
      return { 
        success: false, 
        error: data.overallStatus?.issues?.join(', ') || 'Authentication failed',
        details: data 
      };
    }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Network error' 
    };
  }
}

/**
 * Comprehensive session repair attempt
 */
export async function repairSession(): Promise<{ 
  success: boolean; 
  steps: string[]; 
  finalDiagnostic?: SessionDiagnostic;
  error?: string;
}> {
  const steps: string[] = [];
  
  try {
    // Step 1: Initial diagnosis
    steps.push('🔍 Running initial diagnosis...');
    const initialDiagnostic = await diagnoseSession();
    
    if (initialDiagnostic.hasSession && initialDiagnostic.hasUser && initialDiagnostic.isTokenValid) {
      steps.push('✅ Session is already healthy');
      return { success: true, steps, finalDiagnostic: initialDiagnostic };
    }
    
    // Step 2: Try refreshing session if we have one
    if (initialDiagnostic.hasSession && !initialDiagnostic.isTokenValid) {
      steps.push('🔄 Attempting to refresh expired session...');
      const refreshResult = await refreshSession();
      
      if (refreshResult.success) {
        steps.push('✅ Session refreshed successfully');
        
        // Re-diagnose after refresh
        const postRefreshDiagnostic = await diagnoseSession();
        if (postRefreshDiagnostic.hasSession && postRefreshDiagnostic.isTokenValid) {
          steps.push('✅ Session is now healthy after refresh');
          return { success: true, steps, finalDiagnostic: postRefreshDiagnostic };
        }
      } else {
        steps.push(`❌ Session refresh failed: ${refreshResult.error}`);
      }
    }
    
    // Step 3: Test authenticated call
    steps.push('🧪 Testing authenticated API call...');
    const callTest = await testAuthenticatedCall();
    
    if (callTest.success) {
      steps.push('✅ Authenticated API call successful');
      const finalDiagnostic = await diagnoseSession();
      return { success: true, steps, finalDiagnostic };
    } else {
      steps.push(`❌ Authenticated API call failed: ${callTest.error}`);
    }
    
    // Step 4: If all else fails, recommend clearing state
    steps.push('⚠️ Session repair unsuccessful');
    steps.push('💡 Recommendation: Clear auth state and log in again');
    
    const finalDiagnostic = await diagnoseSession();
    return { 
      success: false, 
      steps, 
      finalDiagnostic,
      error: 'Session could not be repaired automatically' 
    };
    
  } catch (error) {
    steps.push(`💥 Session repair error: ${error}`);
    return { 
      success: false, 
      steps, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Make functions available globally for console debugging
if (typeof window !== 'undefined') {
  (window as any).sessionFixer = {
    diagnoseSession,
    refreshSession,
    clearAuthState,
    testAuthenticatedCall,
    repairSession,
  };
  
  console.log('🔧 Session fixer utilities loaded. Use window.sessionFixer in console:');
  console.log('• window.sessionFixer.diagnoseSession() - Diagnose current session');
  console.log('• window.sessionFixer.refreshSession() - Try to refresh session');
  console.log('• window.sessionFixer.repairSession() - Comprehensive repair attempt');
  console.log('• window.sessionFixer.clearAuthState() - Clear all auth state');
}
