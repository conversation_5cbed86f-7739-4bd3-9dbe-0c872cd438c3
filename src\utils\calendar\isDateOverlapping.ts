import { ReservationType } from "@/types/ReservationType";
import { isUnavailableDate } from "@/utils/calendar/isUnavailableDate";

export const isDateOverlapping = (
  date: Date,
  unavailableDates: ReservationType[],
  selectedReservation?: ReservationType,
  toChange?: "start" | "end"
): boolean => {
  if (!selectedReservation) {
    return isUnavailableDate({ day: date, unavailableDates });
  }

  const tempStart = toChange === "start" ? date : selectedReservation.start;
  const tempEnd = toChange === "end" ? date : selectedReservation.end;

  const startDate = tempStart < tempEnd ? tempStart : tempEnd;
  const endDate = tempStart < tempEnd ? tempEnd : tempStart;

  return unavailableDates.some((reservation) => {
    if (reservation.id === selectedReservation.id) return false;
    return startDate <= reservation.end && endDate >= reservation.start;
  });
};
