import { ExpenseType } from "@/types/ExpenseType";
import { GuestHouseType } from "@/types/GuestHouseType";

export const getGuesthouseExpenses = (
  expenses: ExpenseType[],
  guestHouse: GuestHouseType[]
) => {
  const guesthouseExpenses = <
    { guesthouseID: string; guesthouseName: string; expenses: number }[]
  >[];
  guestHouse.forEach((guesthouse) => {
    const totalExpenses = expenses

      .filter((item) => item.guestHouseId === guesthouse.id)
      .reduce((acc, item) => acc + item.amount, 0);
    guesthouseExpenses.push({
      guesthouseID: guesthouse.id,
      guesthouseName: guesthouse.name,
      expenses: totalExpenses,
    });
  });

  return guesthouseExpenses;
};

export const getTotalExpenses = (expenses: ExpenseType[]) => {
  return expenses.reduce((acc, item) => acc + item.amount, 0);
};
