import { isUnavailableDate } from "@/utils/calendar/isUnavailableDate";
import { isDateOverlapping } from "@/utils/calendar/isDateOverlapping";
import { ReservationType } from "@/types/ReservationType";
import type { Dispatch, SetStateAction } from "react";

type prop = {
  day: Date;
  unavailableDates: ReservationType[];
  selectedReservation?: ReservationType;
  toChange: "start" | "end";
  setToChange: Dispatch<SetStateAction<"start" | "end">>;
  handleReservationChange: (date: Date, toChange: "start" | "end") => void;
};

export const handleDateClick = ({
  day,
  unavailableDates,
  selectedReservation,
  toChange,
  setToChange,
  handleReservationChange,
}: prop) => {
  const isUnavailable = isUnavailableDate({
    day,
    unavailableDates,
  });

  const wouldOverlap = isDateOverlapping(
    day,
    unavailableDates,
    selectedReservation,
    toChange
  );

  if (isUnavailable || wouldOverlap) {
    return;
  }

  let changeType: "start" | "end" = toChange;

  if (selectedReservation) {
    if (day < selectedReservation.start) {
      changeType = "start";
    } else if (day > selectedReservation.end) {
      changeType = "end";
    } else {
      changeType = toChange === "start" ? "end" : "start";
    }
  }

  setToChange(changeType);
  handleReservationChange(day, changeType);
};
